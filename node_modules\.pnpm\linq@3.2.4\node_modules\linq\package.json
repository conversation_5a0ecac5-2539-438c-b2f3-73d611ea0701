{"name": "linq", "author": "<PERSON><PERSON> <<EMAIL>>", "description": "linq.js - LINQ for JavaScript library packaged for node.js", "version": "3.2.4", "license": "MIT", "homepage": "https://github.com/mihaifm/linq", "repository": {"type": "git", "url": "https://github.com/mihaifm/linq.git"}, "scripts": {"test": "node test/testrunner.js", "minify": "uglifyjs linq.js -c -m -o linq.min.js"}, "preferGlobal": false, "keywords": ["linq"], "dependencies": {}, "devDependencies": {"uglify-js": "3.10.3"}, "engines": {"node": "*"}, "main": "./linq", "types": "./linq.d.ts"}