{"name": "openxml-js", "version": "1.1.2", "description": "Unofficial Open XML SDK for node", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/happy-tanuki/openxml-js.git"}, "author": "ma<PERSON><PERSON>", "license": "Microsoft Public License (Ms-PL)", "bugs": {"url": "https://github.com/happy-tanuki/openxml-js/issues"}, "homepage": "https://github.com/happy-tanuki/openxml-js", "dependencies": {"jszip": "^2.4.0", "linq": "^3.0.4-beta", "xmldom": "^0.1.19"}}