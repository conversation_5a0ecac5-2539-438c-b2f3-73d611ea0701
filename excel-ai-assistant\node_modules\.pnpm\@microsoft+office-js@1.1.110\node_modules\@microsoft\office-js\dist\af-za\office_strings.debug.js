if (window.Type && window.Type.registerNamespace) {
Type.registerNamespace('Strings');} else {
if(typeof(window['Strings']) == 'undefined') {
window['Strings'] = new Object(); window['Strings']. __namespace = true;
}

}

Strings.OfficeOM=function Strings_OfficeOM() {
}
if (Strings.OfficeOM.registerClass) Strings.OfficeOM.registerClass('Strings.OfficeOM');
Strings.OfficeOM.L_APICallFailed='API Call Failed';
Strings.OfficeOM.L_APINotSupported='API Not Supported';
Strings.OfficeOM.L_ActivityLimitReached='Activity limit has been reached.';
Strings.OfficeOM.L_AddBindingFromPromptDefaultText='Please make a selection.';
Strings.OfficeOM.L_AddinIsAlreadyRequestingToken='Add-in is already requesting an access token.';
Strings.OfficeOM.L_AddinIsAlreadyRequestingTokenMessage='The operation failed because this add-in is already requesting an access token.';
Strings.OfficeOM.L_ApiNotFoundDetails='The method or property {0} is part of the {1} requirement set, which is not available in your version of {2}.';
Strings.OfficeOM.L_AppNameNotExist='Add-in Name for {0} doesn\'t exist.';
Strings.OfficeOM.L_AppNotExistInitializeNotCalled='Application {0} does not exist. Microsoft.Office.WebExtension.initialize(reason) is not called.';
Strings.OfficeOM.L_AttemptingToSetReadOnlyProperty='Attempting to set read-only property \'{0}\'.';
Strings.OfficeOM.L_BadSelectorString='The string passed into the selector is improperly formatted or unsupported.';
Strings.OfficeOM.L_BindingCreationError='Binding Creation Error';
Strings.OfficeOM.L_BindingNotExist='The specified binding does not exist.';
Strings.OfficeOM.L_BindingToMultipleSelection='Noncontiguous selections are not supported.';
Strings.OfficeOM.L_BrowserAPINotSupported='This browser does not support the requested API.';
Strings.OfficeOM.L_CallbackNotAFunction='Callback must be of type function, was of type {0}.';
Strings.OfficeOM.L_CancelButton='Cancel';
Strings.OfficeOM.L_CannotApplyPropertyThroughSetMethod='Changes to property \'{0}\' cannot be applied through an \"object.set\" method.';
Strings.OfficeOM.L_CannotNavigateTo='The object is located in a place where navigation is not supported.';
Strings.OfficeOM.L_CannotRegisterEvent='The event handler cannot be registered.';
Strings.OfficeOM.L_CannotWriteToSelection='Cannot write to the current selection.';
Strings.OfficeOM.L_CellDataAmountBeyondLimits='Note: The number of cells in a table is suggested to be below 20,000 cells.';
Strings.OfficeOM.L_CellFormatAmountBeyondLimits='Note: The formatting sets set by a Formatting API call is suggested to be below 100.';
Strings.OfficeOM.L_CloseFileBeforeRetrieve='Call closeAsync on the current File before retrieving another.';
Strings.OfficeOM.L_CoercionTypeNotMatchBinding='The specified coercion type is not compatible with this binding type.';
Strings.OfficeOM.L_CoercionTypeNotSupported='The specified coercion type is not supported.';
Strings.OfficeOM.L_ColIndexOutOfRange='The column index value is out of the allowed range. Use a value (0 or higher) that\'s less than the number of columns.';
Strings.OfficeOM.L_ConfirmCancelMessage='Sorry, we can\'t continue.';
Strings.OfficeOM.L_ConfirmDialog='The domain {0} is acting as Microsoft Office and can run Office add-ins, which may access your personal data. If you trust the domain to run as Office and access your data, click OK to continue.';
Strings.OfficeOM.L_ConfirmDialogApiTrustsParent='You are about to send and receive potentially sensitive information from {0}. Only click OK if you trust the following website recieving the sensitive information: {1}.';
Strings.OfficeOM.L_ConfirmDialogConsent='By clicking OK, you will allow {0} access to your content and personal information. ';
Strings.OfficeOM.L_ConfirmDialogConsentTitle='One moment ...';
Strings.OfficeOM.L_ConfirmRefreshMessage='To continue, remove the add-in and add it again, or refresh the page.';
Strings.OfficeOM.L_ConnectionFailureWithDetails='The request failed with status code of {0}, error code {1} and the following error message: {2}';
Strings.OfficeOM.L_ConnectionFailureWithStatus='The request failed with status code of {0}.';
Strings.OfficeOM.L_ContinueButton='Continue';
Strings.OfficeOM.L_CustomFunctionDefinitionMissing='A property with this name that represents the function\'s definition must exist on Excel.Script.CustomFunctions.';
Strings.OfficeOM.L_CustomFunctionImplementationMissing='The property with this name on Excel.Script.CustomFunctions that represents the function\'s definition must contain a \'call\' property that implements the function.';
Strings.OfficeOM.L_CustomFunctionNameCannotSplit='The function name must contain a non-empty namespace and a non-empty short name.';
Strings.OfficeOM.L_CustomFunctionNameContainsBadChars='The function name may only contain letters, digits, underscores, and periods.';
Strings.OfficeOM.L_CustomXmlError='Custom XML Error.';
Strings.OfficeOM.L_CustomXmlExceedQuotaMessage='XPath limits selection to 1024 items.';
Strings.OfficeOM.L_CustomXmlExceedQuotaName='Selection Limit Reached';
Strings.OfficeOM.L_CustomXmlNodeNotFound='The specified node was not found.';
Strings.OfficeOM.L_CustomXmlOutOfDateMessage='The data is out of date. Retrieve the object again.';
Strings.OfficeOM.L_CustomXmlOutOfDateName='Data Not Current';
Strings.OfficeOM.L_DataNotMatchBindingSize='The supplied data object does not match the size of the current selection.';
Strings.OfficeOM.L_DataNotMatchBindingType='The specified data object is not compatible with the binding type.';
Strings.OfficeOM.L_DataNotMatchCoercionType='The type of the specified data object is not compatible with the current selection.';
Strings.OfficeOM.L_DataNotMatchSelection='The supplied data object is not compatible with the shape or dimensions of the current selection.';
Strings.OfficeOM.L_DataReadError='Data Read Error';
Strings.OfficeOM.L_DataStale='Data Not Current';
Strings.OfficeOM.L_DataWriteError='Data Write Error';
Strings.OfficeOM.L_DataWriteReminder='Data Write Reminder';
Strings.OfficeOM.L_DialogAddressNotTrusted='The domain of the URL is not included in the AppDomains element in the manifest, and is not subdomain of source location.';
Strings.OfficeOM.L_DialogAlreadyOpened='The operation failed because this add-in already has an active dialog.';
Strings.OfficeOM.L_DialogInvalidScheme='The URL scheme is not supported. Use HTTPS instead.';
Strings.OfficeOM.L_DialogNavigateError='Dialog Navigation Error';
Strings.OfficeOM.L_DialogOK='OK';
Strings.OfficeOM.L_DialogParentIsMinimized='The operation failed because parent window is minimized.';
Strings.OfficeOM.L_DialogRequireHTTPS='The HTTP protocol is not supported. Use HTTPS instead';
Strings.OfficeOM.L_DisplayDialogError='Display Dialog Error';
Strings.OfficeOM.L_DocumentIsInactive='The operation failed because the document containing this add-in is inactive.';
Strings.OfficeOM.L_DocumentReadOnly='The requested operation is not allowed on the current document mode.';
Strings.OfficeOM.L_ElementMissing='We couldn\'t format the table cell because some parameter values are missing. Double-check the parameters and try again.';
Strings.OfficeOM.L_EventHandlerAdditionFailed='Failed to add the event handler.';
Strings.OfficeOM.L_EventHandlerNotExist='The specified event handler was not found for this binding.';
Strings.OfficeOM.L_EventHandlerRemovalFailed='Failed to remove the event handler.';
Strings.OfficeOM.L_EventRegistrationError='Event Registration Error';
Strings.OfficeOM.L_FileTypeNotSupported='The specified file type is not supported.';
Strings.OfficeOM.L_FormatValueOutOfRange='The value is out of the allowed range.';
Strings.OfficeOM.L_FormattingReminder='Formatting Reminder';
Strings.OfficeOM.L_FunctionCallFailed='Function {0} call failed, error code: {1}.';
Strings.OfficeOM.L_GetDataIsTooLarge='The requested data set is too large.';
Strings.OfficeOM.L_GetDataParametersConflict='The specified parameters conflict.';
Strings.OfficeOM.L_GetSelectionNotSupported='The current selection is not supported.';
Strings.OfficeOM.L_HostError='Host Error';
Strings.OfficeOM.L_ImplicitGetAuthContextMissing='The function to get authentication context is missing';
Strings.OfficeOM.L_ImplicitNotLoaded='The module is not loaded before acquire a token';
Strings.OfficeOM.L_InValidOptionalArgument='invalid optional argument';
Strings.OfficeOM.L_IndexOutOfRange='Index out of range.';
Strings.OfficeOM.L_InitializeNotReady='Office.js has not been fully loaded yet. Please try again later or make sure to add your initialization code on the Office.initialize function.';
Strings.OfficeOM.L_InternalError='Internal Error';
Strings.OfficeOM.L_InternalErrorDescription='An internal error has occurred.';
Strings.OfficeOM.L_InvalidAPICall='Invalid API Call';
Strings.OfficeOM.L_InvalidApiArgumentsMessage='Invalid input arguments.';
Strings.OfficeOM.L_InvalidApiCallInContext='Invalid API call in the current context.';
Strings.OfficeOM.L_InvalidArgument='The argument \'{0}\' doesn\'t work for this situation, is missing, or isn\'t in the right format.';
Strings.OfficeOM.L_InvalidArgumentGeneric='The argument(s) passed into the function don\'t work for this situation, are missing, or aren\'t in the right format.';
Strings.OfficeOM.L_InvalidBinding='Invalid Binding';
Strings.OfficeOM.L_InvalidBindingError='Invalid Binding Error';
Strings.OfficeOM.L_InvalidBindingOperation='Invalid Binding Operation';
Strings.OfficeOM.L_InvalidCellsValue='One or more of the cells parameters have values that aren\'t allowed. Double-check the values and try again.';
Strings.OfficeOM.L_InvalidCoercion='Invalid Coercion Type';
Strings.OfficeOM.L_InvalidColumnsForBinding='The specified columns are invalid.';
Strings.OfficeOM.L_InvalidDataFormat='The format of the specified data object is invalid.';
Strings.OfficeOM.L_InvalidDataObject='Invalid Data Object';
Strings.OfficeOM.L_InvalidFormat='Invalid Format Error';
Strings.OfficeOM.L_InvalidFormatValue='One or more of the format parameters have values that aren\'t allowed. Double-check the values and try again.';
Strings.OfficeOM.L_InvalidGetColumns='The specified columns are invalid.';
Strings.OfficeOM.L_InvalidGetRowColumnCounts='The specified rowCount or columnCount values are invalid.';
Strings.OfficeOM.L_InvalidGetRows='The specified rows are invalid.';
Strings.OfficeOM.L_InvalidGetStartRowColumn='The specified startRow or startColumn values are invalid.';
Strings.OfficeOM.L_InvalidGrant='Preauthorization missing.';
Strings.OfficeOM.L_InvalidGrantMessage='Missing grant for this add-in.';
Strings.OfficeOM.L_InvalidNamedItemForBindingType='The specified binding type is not compatible with the supplied named item.';
Strings.OfficeOM.L_InvalidNode='Invalid Node';
Strings.OfficeOM.L_InvalidObjectPath='The object path \'{0}\' isn\'t working for what you\'re trying to do. If you\'re using the object across multiple \"context.sync\" calls and outside the sequential execution of a \".run\" batch, please use the \"context.trackedObjects.add()\" and \"context.trackedObjects.remove()\" methods to manage the object\'s lifetime.';
Strings.OfficeOM.L_InvalidOperationInCellEditMode='Excel is in cell-editing mode. Please exit the edit mode by pressing ENTER or TAB or selecting another cell, and then try again.';
Strings.OfficeOM.L_InvalidOrTimedOutSession='Invalid Or Timed Out Session';
Strings.OfficeOM.L_InvalidOrTimedOutSessionMessage='Your Office session has expired or is invalid. To continue, refresh the page.';
Strings.OfficeOM.L_InvalidParameters='Function {0} has invalid parameters.';
Strings.OfficeOM.L_InvalidReadForBlankRow='The specified row is blank.';
Strings.OfficeOM.L_InvalidRequestContext='Cannot use the object across different request contexts.';
Strings.OfficeOM.L_InvalidResourceUrl='Invalid application resource Url provided.';
Strings.OfficeOM.L_InvalidResourceUrlMessage='Invalid resource Url specified in the manifest.';
Strings.OfficeOM.L_InvalidSSOAddinMessage='The identity API is not supported for this add-in.';
Strings.OfficeOM.L_InvalidSelectionForBindingType='A binding cannot be created with the current selection and the specified binding type.';
Strings.OfficeOM.L_InvalidSetColumns='The specified columns are invalid.';
Strings.OfficeOM.L_InvalidSetRows='The specified rows are invalid.';
Strings.OfficeOM.L_InvalidSetStartRowColumn='The specified startRow or startColumn values are invalid.';
Strings.OfficeOM.L_InvalidTableOptionValue='One or more of the tableOptions parameters have values that aren\'t allowed. Double-check the values and try again.';
Strings.OfficeOM.L_InvalidValue='Invalid Value';
Strings.OfficeOM.L_MemoryLimit='Memory Limit Exceeded';
Strings.OfficeOM.L_MissingParameter='Missing Parameter';
Strings.OfficeOM.L_MissingRequiredArguments='missing some required arguments';
Strings.OfficeOM.L_ModalDialogOpeng='The operation failed because this add-in already has an active modal dialog.';
Strings.OfficeOM.L_MultipleNamedItemFound='Multiple objects with the same name were found.';
Strings.OfficeOM.L_NamedItemNotFound='The named item does not exist.';
Strings.OfficeOM.L_NavOutOfBound='The operation failed because the index is out of range.';
Strings.OfficeOM.L_NetworkProblem='Network Problem';
Strings.OfficeOM.L_NetworkProblemRetrieveFile='A network problem has prevented retrieval of the file.';
Strings.OfficeOM.L_NewWindowCrossZone='The security settings in your browser prevent us from creating a dialog box. Try a different browser, or {0} so that \'{1}\' and the domain shown in your address bar are in the same security zone.';
Strings.OfficeOM.L_NewWindowCrossZoneConfigureBrowserLink='configure your browser';
Strings.OfficeOM.L_NewWindowCrossZoneErrorString='Browser restrictions prevented us from creating the dialog box. The domain of dialog box and the domain of the add-in host are not in the same security zone.';
Strings.OfficeOM.L_NoCapability='You don\'t have sufficient permissions for this action.';
Strings.OfficeOM.L_NoHttpsWAC='This Office session is not using a secure connection. We recommend that you take extra precautions.';
Strings.OfficeOM.L_NonUniformPartialGetNotSupported='Coordinate parameters cannot be used with coercion type Table when the table contains merged cells.';
Strings.OfficeOM.L_NonUniformPartialSetNotSupported='Coordinate parameters cannot be used with coercion type Table when the table contains merged cells.';
Strings.OfficeOM.L_NotImplemented='Function {0} is not implemented.';
Strings.OfficeOM.L_NotSupported='Function {0} is not supported.';
Strings.OfficeOM.L_NotSupportedBindingType='The specified binding type {0} is not supported.';
Strings.OfficeOM.L_NotSupportedEventType='The specified event type {0} is not supported.';
Strings.OfficeOM.L_NotTrustedWAC='This add-in has been disabled to help keep you safe. To continue using the add-in, validate that this item is hosted in a trusted domain or open it in the Office desktop app.';
Strings.OfficeOM.L_OKButton='OK';
Strings.OfficeOM.L_OperationCancelledError='Operation Cancelled';
Strings.OfficeOM.L_OperationCancelledErrorMessage='The operation was cancelled by the user.';
Strings.OfficeOM.L_OperationNotSupported='The operation is not supported.';
Strings.OfficeOM.L_OperationNotSupportedOnMatrixData='Selected content needs to be in table format. Format the data as a table and try again.';
Strings.OfficeOM.L_OperationNotSupportedOnThisBindingType='Operation is not supported on this binding type.';
Strings.OfficeOM.L_OsfControlTypeNotSupported='OsfControl type not supported.';
Strings.OfficeOM.L_OutOfRange='Out of Range';
Strings.OfficeOM.L_OverwriteWorksheetData='The set operation failed because the supplied data object will overwrite or shift data.';
Strings.OfficeOM.L_PermissionDenied='Permission Denied';
Strings.OfficeOM.L_PropertyDoesNotExist='Property \'{0}\' does not exist on the object.';
Strings.OfficeOM.L_PropertyNotLoaded='The property \'{0}\' is not available. Before reading the property\'s value, call the load method on the containing object and call \"context.sync()\" on the associated request context.';
Strings.OfficeOM.L_ReadSettingsError='Read Settings Error';
Strings.OfficeOM.L_RedundantCallbackSpecification='Callback cannot be specified both in argument list and in optional object.';
Strings.OfficeOM.L_RequestPayloadSizeLimitExceededMessage='The request payload size has exceeded the limit. Please refer to the documentation: \"https://docs.microsoft.com/office/dev/add-ins/concepts/resource-limits-and-performance-optimization#excel-add-ins\".';
Strings.OfficeOM.L_RequestTimeout='The call took too long to execute.';
Strings.OfficeOM.L_RequestTokenUnavailable='This API has been throttled to slow the call frequency.';
Strings.OfficeOM.L_ResponsePayloadSizeLimitExceededMessage='The response payload size has exceeded the limit. Please refer to the documentation: \"https://docs.microsoft.com/office/dev/add-ins/concepts/resource-limits-and-performance-optimization#excel-add-ins\".';
Strings.OfficeOM.L_RowIndexOutOfRange='The row index value is out of the allowed range. Use a value (0 or higher) that\'s less than the number of rows.';
Strings.OfficeOM.L_RunMustReturnPromise='The batch function passed to the \".run\" method didn\'t return a promise. The function must return a promise, so that any automatically-tracked objects can be released at the completion of the batch operation. Typically, you return a promise by returning the response from \"context.sync()\".';
Strings.OfficeOM.L_SSOClientError='Error occurred in the authentication request from Office.';
Strings.OfficeOM.L_SSOClientErrorMessage='An unexpected error occurred in the client.';
Strings.OfficeOM.L_SSOConnectionLostError='A connection was lost during the sign in process.';
Strings.OfficeOM.L_SSOConnectionLostErrorMessage='A connection was lost during the sign in process, and the user may not be signed in. This was likely due to the user\'s browser configuration settings, such as security zones.';
Strings.OfficeOM.L_SSOServerError='Error occurred in the authentication provider.';
Strings.OfficeOM.L_SSOServerErrorMessage='An unexpected error occurred on the server.';
Strings.OfficeOM.L_SSOUnsupportedPlatform='API is not supported in this platform.';
Strings.OfficeOM.L_SSOUserConsentNotSupportedByCurrentAddinCategory='This add-in does not support user consent.';
Strings.OfficeOM.L_SSOUserConsentNotSupportedByCurrentAddinCategoryMessage='The operation failed because this add-in does not support user consent in this category';
Strings.OfficeOM.L_SaveSettingsError='Save Settings Error';
Strings.OfficeOM.L_SelectionCannotBound='Cannot bind to the current selection.';
Strings.OfficeOM.L_SelectionNotSupportCoercionType='The current selection is not compatible with the specified coercion type.';
Strings.OfficeOM.L_SetDataIsTooLarge='The specified data object is too large.';
Strings.OfficeOM.L_SetDataParametersConflict='The specified parameters conflict.';
Strings.OfficeOM.L_SettingNameNotExist='The specified setting name does not exist.';
Strings.OfficeOM.L_SettingsAreStale='Settings could not be saved because they are not current.';
Strings.OfficeOM.L_SettingsCannotSave='The settings could not be saved.';
Strings.OfficeOM.L_SettingsStaleError='Settings Stale Error';
Strings.OfficeOM.L_ShowWindowDialogNotification='{0} wants to display a new window.';
Strings.OfficeOM.L_ShowWindowDialogNotificationAllow='Allow';
Strings.OfficeOM.L_ShowWindowDialogNotificationIgnore='Ignore';
Strings.OfficeOM.L_ShuttingDown='Operation failed because the data is not current on the server.';
Strings.OfficeOM.L_SliceSizeNotSupported='The specified slice size is not supported.';
Strings.OfficeOM.L_SpecifiedIdNotExist='The specified ID does not exist.';
Strings.OfficeOM.L_Timeout='The operation has timed out.';
Strings.OfficeOM.L_TooManyArguments='too many arguments';
Strings.OfficeOM.L_TooManyIncompleteRequests='Wait until the previous call completes.';
Strings.OfficeOM.L_TooManyOptionalFunction='multiple optional functions in parameter list';
Strings.OfficeOM.L_TooManyOptionalObjects='multiple optional objects in parameter list';
Strings.OfficeOM.L_UnknownBindingType='The binding type is not supported.';
Strings.OfficeOM.L_UnsupportedDataObject='The supplied data object type is not supported.';
Strings.OfficeOM.L_UnsupportedEnumeration='Unsupported Enumeration';
Strings.OfficeOM.L_UnsupportedEnumerationMessage='The enumeration isn\'t supported in the current host application.';
Strings.OfficeOM.L_UnsupportedUserIdentity='User identity type is not supported.';
Strings.OfficeOM.L_UnsupportedUserIdentityMessage='The identity type of the user is not supported.';
Strings.OfficeOM.L_UserAborted='User aborted the consent request.';
Strings.OfficeOM.L_UserAbortedMessage='The user did not consent the add-in permissions.';
Strings.OfficeOM.L_UserClickIgnore='The user chose to ignore the dialog box.';
Strings.OfficeOM.L_UserNotSignedIn='No user is signed into Office.';
Strings.OfficeOM.L_ValueNotLoaded='The value of the result object has not been loaded yet. Before reading the value property, call \"context.sync()\" on the associated request context.';
Strings.OfficeOM.L_WorkbookHiddenMessage='The JavaScript API request failed because the workbook was hidden. Please unhide the workbook and try again.';
Strings.OfficeOM.L_WriteNotSupportedWhenModalDialogOpen='Write operation is not supported for Office when a modal dialog is open.';

