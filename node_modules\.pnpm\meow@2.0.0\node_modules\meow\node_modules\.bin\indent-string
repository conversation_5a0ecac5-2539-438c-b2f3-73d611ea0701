#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/indent-string@1.2.2/node_modules/indent-string/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/indent-string@1.2.2/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/indent-string@1.2.2/node_modules/indent-string/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/indent-string@1.2.2/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../indent-string@1.2.2/node_modules/indent-string/cli.js" "$@"
else
  exec node  "$basedir/../../../../../indent-string@1.2.2/node_modules/indent-string/cli.js" "$@"
fi
