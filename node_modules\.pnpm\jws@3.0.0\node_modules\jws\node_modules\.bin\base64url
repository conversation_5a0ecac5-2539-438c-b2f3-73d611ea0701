#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/base64url@1.0.6/node_modules/base64url/bin/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/base64url@1.0.6/node_modules/base64url/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/base64url@1.0.6/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/base64url@1.0.6/node_modules/base64url/bin/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/base64url@1.0.6/node_modules/base64url/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/base64url@1.0.6/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../base64url@1.0.6/node_modules/base64url/bin/base64url" "$@"
else
  exec node  "$basedir/../../../../../base64url@1.0.6/node_modules/base64url/bin/base64url" "$@"
fi
