!function(t,s){function l(t,e,n){var r=new v,o=a;this.current=r.current,this.moveNext=function(){try{switch(o){case a:o=c,t();case c:return!!e.apply(r)||(this.dispose(),!1);case f:return!1}}catch(t){throw this.dispose(),t}},this.dispose=function(){if(o==c)try{n()}finally{o=f}}}function p(t){this.getEnumerator=t}var h={Identity:function(t){return t},True:function(){return!0},Blank:function(){}},o=typeof!0,u="number",y="string",e=typeof{},r=typeof s,i="function",d={"":h.Identity},m={createLambda:function(t){if(null==t)return h.Identity;if(typeof t!==y)return t;if(null!=(s=d[t]))return s;if(-1===t.indexOf("=>")){for(var e,n=new RegExp("[$]+","g"),r=0;null!=(e=n.exec(t));){var o=e[0].length;r<o&&(r=o)}for(var u=[],i=1;i<=r;i++){for(var a="",c=0;c<i;c++)a+="$";u.push(a)}var f=Array.prototype.join.call(u,","),s=new Function(f,"return "+t);return d[t]=s}var l=t.match(/^[(\s]*([^()]*?)[)\s]*=>(.*)/);return s=new Function(l[1],l[2].match(/\breturn\b/)?l[2]:"return "+l[2]),d[t]=s},isIEnumerable:function(t){if(typeof Enumerator!==r)try{return new Enumerator(t),!0}catch(t){}return!1},defineProperty:null!=Object.defineProperties?function(t,e,n){Object.defineProperty(t,e,{enumerable:!1,configurable:!0,writable:!0,value:n})}:function(t,e,n){t[e]=n},compare:function(t,e){return t===e?0:e<t?1:-1},dispose:function(t){null!=t&&t.dispose()},hasNativeIteratorSupport:function(){return"undefined"!=typeof Symbol&&void 0!==Symbol.iterator}},a=0,c=1,f=2,v=function(){var e=null;this.current=function(){return e},this.yieldReturn=function(t){return e=t,!0},this.yieldBreak=function(){return!1}};(p.Utils={}).createLambda=function(t){return m.createLambda(t)},p.Utils.createEnumerable=function(t){return new p(t)},p.Utils.createEnumerator=function(t,e,n){return new l(t,e,n)},p.Utils.extendTo=function(t){var e,n=t.prototype;for(var r in t===Array?(e=b.prototype,m.defineProperty(n,"getSource",function(){return this})):(e=p.prototype,m.defineProperty(n,"getEnumerator",function(){return p.from(this).getEnumerator()})),e){var o=e[r];n[r]!=o&&(null!=n[r]&&n[r+="ByLinq"]==o||o instanceof Function&&m.defineProperty(n,r,o))}},p.Utils.recallFrom=function(t){var e,n=t.prototype;for(var r in t===Array?(e=b.prototype,delete n.getSource):(e=p.prototype,delete n.getEnumerator),e){var o=e[r];n[r+"ByLinq"]?delete n[r+"ByLinq"]:n[r]==o&&o instanceof Function&&delete n[r]}},p.choice=function(){var t=arguments;return new p(function(){return new l(function(){t=t[0]instanceof Array?t[0]:null!=t[0].getEnumerator?t[0].toArray():t},function(){return this.yieldReturn(t[Math.floor(Math.random()*t.length)])},h.Blank)})},p.cycle=function(){var e=arguments;return new p(function(){var t=0;return new l(function(){e=e[0]instanceof Array?e[0]:null!=e[0].getEnumerator?e[0].toArray():e},function(){return t>=e.length&&(t=0),this.yieldReturn(e[t++])},h.Blank)})},p.empty=function(){return new p(function(){return new l(h.Blank,function(){return!1},h.Blank)})},p.from=function(r){if(null==r)return p.empty();if(r instanceof p)return r;if(typeof r==u||typeof r==o)return p.repeat(r,1);if(typeof r==y)return new p(function(){var t=0;return new l(h.Blank,function(){return t<r.length&&this.yieldReturn(r.charAt(t++))},h.Blank)});if(typeof r!=i){if(typeof r.length==u)return new b(r);if("undefined"!=typeof Symbol&&void 0!==r[Symbol.iterator])return new p(function(){return new l(h.Blank,function(){var t=r.next();return!t.done&&this.yieldReturn(t.value)},h.Blank)});if(!(r instanceof Object)&&m.isIEnumerable(r))return new p(function(){var t,e=!0;return new l(function(){t=new Enumerator(r)},function(){return e?e=!1:t.moveNext(),!t.atEnd()&&this.yieldReturn(t.item())},h.Blank)});if(typeof Windows===e&&typeof r.first===i)return new p(function(){var t,e=!0;return new l(function(){t=r.first()},function(){return e?e=!1:t.moveNext(),t.hasCurrent?this.yieldReturn(t.current):this.yieldBreak()},h.Blank)})}return new p(function(){var n=[],t=0;return new l(function(){for(var t in r){var e=r[t];e instanceof Function||!Object.prototype.hasOwnProperty.call(r,t)||n.push({key:t,value:e})}},function(){return t<n.length&&this.yieldReturn(n[t++])},h.Blank)})},p.make=function(t){return p.repeat(t,1)},p.matches=function(n,t,r){return null==r&&(r=""),t instanceof RegExp&&(r+=t.ignoreCase?"i":"",r+=t.multiline?"m":"",t=t.source),-1===r.indexOf("g")&&(r+="g"),new p(function(){var e;return new l(function(){e=new RegExp(t,r)},function(){var t=e.exec(n);return!!t&&this.yieldReturn(t)},h.Blank)})},p.range=function(n,r,o){return null==o&&(o=1),new p(function(){var t,e=0;return new l(function(){t=n-o},function(){return e++<r?this.yieldReturn(t+=o):this.yieldBreak()},h.Blank)})},p.rangeDown=function(n,r,o){return null==o&&(o=1),new p(function(){var t,e=0;return new l(function(){t=n+o},function(){return e++<r?this.yieldReturn(t-=o):this.yieldBreak()},h.Blank)})},p.rangeTo=function(t,n,r){return null==r&&(r=1),new p(t<n?function(){var e;return new l(function(){e=t-r},function(){var t=e+=r;return t<=n?this.yieldReturn(t):this.yieldBreak()},h.Blank)}:function(){var e;return new l(function(){e=t+r},function(){var t=e-=r;return n<=t?this.yieldReturn(t):this.yieldBreak()},h.Blank)})},p.repeat=function(t,e){return null!=e?p.repeat(t).take(e):new p(function(){return new l(h.Blank,function(){return this.yieldReturn(t)},h.Blank)})},p.repeatWithFinalize=function(e,n){return e=m.createLambda(e),n=m.createLambda(n),new p(function(){var t;return new l(function(){t=e()},function(){return this.yieldReturn(t)},function(){null!=t&&(n(t),t=null)})})},p.generate=function(t,e){return null!=e?p.generate(t).take(e):(t=m.createLambda(t),new p(function(){return new l(h.Blank,function(){return this.yieldReturn(t())},h.Blank)}))},p.toInfinity=function(e,n){return null==e&&(e=0),null==n&&(n=1),new p(function(){var t;return new l(function(){t=e-n},function(){return this.yieldReturn(t+=n)},h.Blank)})},p.toNegativeInfinity=function(e,n){return null==e&&(e=0),null==n&&(n=1),new p(function(){var t;return new l(function(){t=e+n},function(){return this.yieldReturn(t-=n)},h.Blank)})},p.unfold=function(n,r){return r=m.createLambda(r),new p(function(){var t,e=!0;return new l(h.Blank,function(){return t=e?(e=!1,n):r(t),this.yieldReturn(t)},h.Blank)})},p.defer=function(e){return new p(function(){var t;return new l(function(){t=p.from(e()).getEnumerator()},function(){return t.moveNext()?this.yieldReturn(t.current()):this.yieldBreak()},function(){m.dispose(t)})})},p.prototype.traverseBreadthFirst=function(o,u){var t=this;return o=m.createLambda(o),u=m.createLambda(u),new p(function(){var e,n=0,r=[];return new l(function(){e=t.getEnumerator()},function(){for(;;){if(e.moveNext())return r.push(e.current()),this.yieldReturn(u(e.current(),n));var t=p.from(r).selectMany(function(t){return o(t)});if(!t.any())return!1;n++,r=[],m.dispose(e),e=t.getEnumerator()}},function(){m.dispose(e)})})},p.prototype.traverseDepthFirst=function(r,o){var t=this;return r=m.createLambda(r),o=m.createLambda(o),new p(function(){var e,n=[];return new l(function(){e=t.getEnumerator()},function(){for(;;){if(e.moveNext()){var t=o(e.current(),n.length);return n.push(e),e=p.from(r(e.current())).getEnumerator(),this.yieldReturn(t)}if(n.length<=0)return!1;m.dispose(e),e=n.pop()}},function(){try{m.dispose(e)}finally{p.from(n).forEach(function(t){t.dispose()})}})})},p.prototype.flatten=function(){var n=this;return new p(function(){var t,e=null;return new l(function(){t=n.getEnumerator()},function(){for(;;){if(null!=e){if(e.moveNext())return this.yieldReturn(e.current());e=null}if(t.moveNext()){if(t.current()instanceof Array){m.dispose(e),e=p.from(t.current()).selectMany(h.Identity).flatten().getEnumerator();continue}return this.yieldReturn(t.current())}return!1}},function(){try{m.dispose(t)}finally{m.dispose(e)}})})},p.prototype.pairwise=function(n){var t=this;return n=m.createLambda(n),new p(function(){var e;return new l(function(){(e=t.getEnumerator()).moveNext()},function(){var t=e.current();return!!e.moveNext()&&this.yieldReturn(n(t,e.current()))},function(){m.dispose(e)})})},p.prototype.scan=function(r,o){var u=null==o?(o=m.createLambda(r),!1):(o=m.createLambda(o),!0),i=this;return new p(function(){var t,e,n=!0;return new l(function(){t=i.getEnumerator()},function(){if(n){if(n=!1,u)return this.yieldReturn(e=r);if(t.moveNext())return this.yieldReturn(e=t.current())}return!!t.moveNext()&&this.yieldReturn(e=o(e,t.current()))},function(){m.dispose(t)})})},p.prototype.select=function(n){if((n=m.createLambda(n)).length<=1)return new x(this,null,n);var r=this;return new p(function(){var t,e=0;return new l(function(){t=r.getEnumerator()},function(){return!!t.moveNext()&&this.yieldReturn(n(t.current(),e++))},function(){m.dispose(t)})})},p.prototype.selectMany=function(o,u){var t=this;return o=m.createLambda(o),null==u&&(u=function(t,e){return e}),u=m.createLambda(u),new p(function(){var e,n=s,r=0;return new l(function(){e=t.getEnumerator()},function(){if(n===s&&!e.moveNext())return!1;do{var t;if(null==n&&(t=o(e.current(),r++),n=p.from(t).getEnumerator()),n.moveNext())return this.yieldReturn(u(e.current(),n.current()));m.dispose(n),n=null}while(e.moveNext());return!1},function(){try{m.dispose(e)}finally{m.dispose(n)}})})},p.prototype.where=function(n){if((n=m.createLambda(n)).length<=1)return new k(this,n);var r=this;return new p(function(){var t,e=0;return new l(function(){t=r.getEnumerator()},function(){for(;t.moveNext();)if(n(t.current(),e++))return this.yieldReturn(t.current());return!1},function(){m.dispose(t)})})},p.prototype.choose=function(r){r=m.createLambda(r);var t=this;return new p(function(){var e,n=0;return new l(function(){e=t.getEnumerator()},function(){for(;e.moveNext();){var t=r(e.current(),n++);if(null!=t)return this.yieldReturn(t)}return this.yieldBreak()},function(){m.dispose(e)})})},p.prototype.ofType=function(e){var n;switch(e){case Number:n=u;break;case String:n=y;break;case Boolean:n=o;break;case Function:n=i;break;default:n=null}return null===n?this.where(function(t){return t instanceof e}):this.where(function(t){return typeof t===n})},p.prototype.zip=function(){var r=arguments,o=m.createLambda(arguments[arguments.length-1]),u=this;if(2!=arguments.length)return new p(function(){var e,n=0;return new l(function(){var t=p.make(u).concat(p.from(r).takeExceptLast().select(p.from)).select(function(t){return t.getEnumerator()}).toArray();e=p.from(t)},function(){if(e.all(function(t){return t.moveNext()})){var t=e.select(function(t){return t.current()}).toArray();return t.push(n++),this.yieldReturn(o.apply(null,t))}return this.yieldBreak()},function(){p.from(e).forEach(m.dispose)})});var i=arguments[0];return new p(function(){var t,e,n=0;return new l(function(){t=u.getEnumerator(),e=p.from(i).getEnumerator()},function(){return!(!t.moveNext()||!e.moveNext())&&this.yieldReturn(o(t.current(),e.current(),n++))},function(){try{m.dispose(t)}finally{m.dispose(e)}})})},p.prototype.merge=function(){var t=arguments,r=this;return new p(function(){var e,n=-1;return new l(function(){e=p.make(r).concat(p.from(t).select(p.from)).select(function(t){return t.getEnumerator()}).toArray()},function(){for(;0<e.length;){n=n>=e.length-1?0:n+1;var t=e[n];if(t.moveNext())return this.yieldReturn(t.current());t.dispose(),e.splice(n--,1)}return this.yieldBreak()},function(){p.from(e).forEach(m.dispose)})})},p.prototype.join=function(t,i,e,a,c){i=m.createLambda(i),e=m.createLambda(e),a=m.createLambda(a),c=m.createLambda(c);var f=this;return new p(function(){var n,r,o=null,u=0;return new l(function(){n=f.getEnumerator(),r=p.from(t).toLookup(e,h.Identity,c)},function(){for(;;){if(null!=o){var t=o[u++];if(t!==s)return this.yieldReturn(a(n.current(),t));t=null,u=0}if(!n.moveNext())return!1;var e=i(n.current());o=r.get(e).toArray()}},function(){m.dispose(n)})})},p.prototype.groupJoin=function(t,r,o,u,i){r=m.createLambda(r),o=m.createLambda(o),u=m.createLambda(u),i=m.createLambda(i);var a=this;return new p(function(){var e=a.getEnumerator(),n=null;return new l(function(){e=a.getEnumerator(),n=p.from(t).toLookup(o,h.Identity,i)},function(){if(e.moveNext()){var t=n.get(r(e.current()));return this.yieldReturn(u(e.current(),t))}return!1},function(){m.dispose(e)})})},p.prototype.all=function(e){e=m.createLambda(e);var n=!0;return this.forEach(function(t){if(!e(t))return n=!1}),n},p.prototype.any=function(t){t=m.createLambda(t);var e=this.getEnumerator();try{if(0==arguments.length)return e.moveNext();for(;e.moveNext();)if(t(e.current()))return!0;return!1}finally{m.dispose(e)}},p.prototype.isEmpty=function(){return!this.any()},p.prototype.concat=function(){var n=this;if(1==arguments.length){var r=arguments[0];return new p(function(){var t,e;return new l(function(){t=n.getEnumerator()},function(){if(null==e){if(t.moveNext())return this.yieldReturn(t.current());e=p.from(r).getEnumerator()}return!!e.moveNext()&&this.yieldReturn(e.current())},function(){try{m.dispose(t)}finally{m.dispose(e)}})})}var t=arguments;return new p(function(){var e;return new l(function(){e=p.make(n).concat(p.from(t).select(p.from)).select(function(t){return t.getEnumerator()}).toArray()},function(){for(;0<e.length;){var t=e[0];if(t.moveNext())return this.yieldReturn(t.current());t.dispose(),e.splice(0,1)}return this.yieldBreak()},function(){p.from(e).forEach(m.dispose)})})},p.prototype.insert=function(o,u){var i=this;return new p(function(){var t,e,n=0,r=!1;return new l(function(){t=i.getEnumerator(),e=p.from(u).getEnumerator()},function(){return n==o&&e.moveNext()?(r=!0,this.yieldReturn(e.current())):t.moveNext()?(n++,this.yieldReturn(t.current())):!(r||!e.moveNext())&&this.yieldReturn(e.current())},function(){try{m.dispose(t)}finally{m.dispose(e)}})})},p.prototype.alternate=function(t){var u=this;return new p(function(){var e,n,r,o;return new l(function(){r=t instanceof Array||null!=t.getEnumerator?p.from(p.from(t).toArray()):p.make(t),(n=u.getEnumerator()).moveNext()&&(e=n.current())},function(){for(;;){if(null!=o){if(o.moveNext())return this.yieldReturn(o.current());o=null}if(null!=e||!n.moveNext()){if(null==e)return this.yieldBreak();var t=e;return e=null,this.yieldReturn(t)}e=n.current(),o=r.getEnumerator()}},function(){try{m.dispose(n)}finally{m.dispose(o)}})})},p.prototype.contains=function(t,e){e=m.createLambda(e);var n=this.getEnumerator();try{for(;n.moveNext();)if(e(n.current())===t)return!0;return!1}finally{m.dispose(n)}},p.prototype.defaultIfEmpty=function(n){var r=this;return n===s&&(n=null),new p(function(){var t,e=!0;return new l(function(){t=r.getEnumerator()},function(){return t.moveNext()?(e=!1,this.yieldReturn(t.current())):!!e&&(e=!1,this.yieldReturn(n))},function(){m.dispose(t)})})},p.prototype.distinct=function(t){return this.except(p.empty(),t)},p.prototype.distinctUntilChanged=function(o){o=m.createLambda(o);var t=this;return new p(function(){var e,n,r;return new l(function(){e=t.getEnumerator()},function(){for(;e.moveNext();){var t=o(e.current());if(r)return r=!1,n=t,this.yieldReturn(e.current());if(n!==t)return n=t,this.yieldReturn(e.current())}return this.yieldBreak()},function(){m.dispose(e)})})},p.prototype.except=function(t,r){r=m.createLambda(r);var o=this;return new p(function(){var e,n;return new l(function(){e=o.getEnumerator(),n=new L(r),p.from(t).forEach(function(t){n.add(t)})},function(){for(;e.moveNext();){var t=e.current();if(!n.contains(t))return n.add(t),this.yieldReturn(t)}return!1},function(){m.dispose(e)})})},p.prototype.intersect=function(t,o){o=m.createLambda(o);var u=this;return new p(function(){var e,n,r;return new l(function(){e=u.getEnumerator(),n=new L(o),p.from(t).forEach(function(t){n.add(t)}),r=new L(o)},function(){for(;e.moveNext();){var t=e.current();if(!r.contains(t)&&n.contains(t))return r.add(t),this.yieldReturn(t)}return!1},function(){m.dispose(e)})})},p.prototype.sequenceEqual=function(t,e){e=m.createLambda(e);var n=this.getEnumerator();try{var r=p.from(t).getEnumerator();try{for(;n.moveNext();)if(!r.moveNext()||e(n.current())!==e(r.current()))return!1;return r.moveNext()?!1:!0}finally{m.dispose(r)}}finally{m.dispose(n)}},p.prototype.union=function(o,t){t=m.createLambda(t);var u=this;return new p(function(){var e,n,r;return new l(function(){e=u.getEnumerator(),r=new L(t)},function(){var t;if(n===s){for(;e.moveNext();)if(t=e.current(),!r.contains(t))return r.add(t),this.yieldReturn(t);n=p.from(o).getEnumerator()}for(;n.moveNext();)if(t=n.current(),!r.contains(t))return r.add(t),this.yieldReturn(t);return!1},function(){try{m.dispose(e)}finally{m.dispose(n)}})})},p.prototype.orderBy=function(t,e){return new w(this,t,e,!1)},p.prototype.orderByDescending=function(t,e){return new w(this,t,e,!0)},p.prototype.reverse=function(){var n=this;return new p(function(){var t,e;return new l(function(){t=n.toArray(),e=t.length},function(){return 0<e&&this.yieldReturn(t[--e])},h.Blank)})},p.prototype.shuffle=function(){var t=this;return new p(function(){var e;return new l(function(){e=t.toArray()},function(){if(0<e.length){var t=Math.floor(Math.random()*e.length);return this.yieldReturn(e.splice(t,1)[0])}return!1},h.Blank)})},p.prototype.weightedSample=function(n){n=m.createLambda(n);var t=this;return new p(function(){var o,u=0;return new l(function(){o=t.choose(function(t){var e=n(t);return e<=0?null:{value:t,bound:u+=e}}).toArray()},function(){if(0<o.length){for(var t=Math.floor(Math.random()*u)+1,e=-1,n=o.length;1<n-e;){var r=Math.floor((e+n)/2);o[r].bound>=t?n=r:e=r}return this.yieldReturn(o[n].value)}return this.yieldBreak()},h.Blank)})},p.prototype.groupBy=function(e,n,r,o){var u=this;return e=m.createLambda(e),n=m.createLambda(n),null!=r&&(r=m.createLambda(r)),o=m.createLambda(o),new p(function(){var t;return new l(function(){t=u.toLookup(e,n,o).toEnumerable().getEnumerator()},function(){return!!t.moveNext()&&(null==r?this.yieldReturn(t.current()):this.yieldReturn(r(t.current().key(),t.current())))},function(){m.dispose(t)})})},p.prototype.partitionBy=function(i,a,c,f){var s,t=this;return i=m.createLambda(i),a=m.createLambda(a),f=m.createLambda(f),c=null==c?(s=!1,function(t,e){return new N(t,e)}):(s=!0,m.createLambda(c)),new p(function(){var n,r,o,u=[];return new l(function(){(n=t.getEnumerator()).moveNext()&&(r=i(n.current()),o=f(r),u.push(a(n.current())))},function(){for(var t;1==(t=n.moveNext())&&o===f(i(n.current()));)u.push(a(n.current()));if(0<u.length){var e=c(r,s?p.from(u):u);return u=t?(r=i(n.current()),o=f(r),[a(n.current())]):[],this.yieldReturn(e)}return!1},function(){m.dispose(n)})})},p.prototype.buffer=function(r){var t=this;return new p(function(){var n;return new l(function(){n=t.getEnumerator()},function(){for(var t=[],e=0;n.moveNext();)if(t.push(n.current()),++e>=r)return this.yieldReturn(t);return 0<t.length&&this.yieldReturn(t)},function(){m.dispose(n)})})},p.prototype.aggregate=function(t,e,n){return(n=m.createLambda(n))(this.scan(t,e,n).last())},p.prototype.average=function(e){e=m.createLambda(e);var n=0,r=0;return this.forEach(function(t){n+=e(t),++r}),n/r},p.prototype.count=function(n){n=null==n?h.True:m.createLambda(n);var r=0;return this.forEach(function(t,e){n(t,e)&&++r}),r},p.prototype.max=function(t){return null==t&&(t=h.Identity),this.select(t).aggregate(function(t,e){return e<t?t:e})},p.prototype.min=function(t){return null==t&&(t=h.Identity),this.select(t).aggregate(function(t,e){return t<e?t:e})},p.prototype.maxBy=function(n){return n=m.createLambda(n),this.aggregate(function(t,e){return n(t)>n(e)?t:e})},p.prototype.minBy=function(n){return n=m.createLambda(n),this.aggregate(function(t,e){return n(t)<n(e)?t:e})},p.prototype.sum=function(t){return null==t&&(t=h.Identity),this.select(t).aggregate(0,function(t,e){return t+e})},p.prototype.elementAt=function(n){var r,o=!1;if(this.forEach(function(t,e){if(e==n)return r=t,!(o=!0)}),!o)throw new Error("index is less than 0 or greater than or equal to the number of elements in source.");return r},p.prototype.elementAtOrDefault=function(n,t){var r;t===s&&(t=null);var o=!1;return this.forEach(function(t,e){if(e==n)return r=t,!(o=!0)}),o?r:t},p.prototype.first=function(t){if(null!=t)return this.where(t).first();var e,n=!1;if(this.forEach(function(t){return e=t,!(n=!0)}),!n)throw new Error("first:No element satisfies the condition.");return e},p.prototype.firstOrDefault=function(t,e){if(t!==s){if(typeof t===i||typeof m.createLambda(t)===i)return this.where(t).firstOrDefault(s,e);e=t}var n,r=!1;return this.forEach(function(t){return n=t,!(r=!0)}),r?n:e},p.prototype.last=function(t){if(null!=t)return this.where(t).last();var e,n=!1;if(this.forEach(function(t){n=!0,e=t}),!n)throw new Error("last:No element satisfies the condition.");return e},p.prototype.lastOrDefault=function(t,e){if(t!==s){if(typeof t===i||typeof m.createLambda(t)===i)return this.where(t).lastOrDefault(s,e);e=t}var n,r=!1;return this.forEach(function(t){r=!0,n=t}),r?n:e},p.prototype.single=function(t){if(null!=t)return this.where(t).single();var e,n=!1;if(this.forEach(function(t){if(n)throw new Error("single:sequence contains more than one element.");n=!0,e=t}),!n)throw new Error("single:No element satisfies the condition.");return e},p.prototype.singleOrDefault=function(t,e){if(e===s&&(e=null),null!=t)return this.where(t).singleOrDefault(null,e);var n,r=!1;return this.forEach(function(t){if(r)throw new Error("single:sequence contains more than one element.");r=!0,n=t}),r?n:e},p.prototype.skip=function(n){var r=this;return new p(function(){var t,e=0;return new l(function(){for(t=r.getEnumerator();e++<n&&t.moveNext(););},function(){return!!t.moveNext()&&this.yieldReturn(t.current())},function(){m.dispose(t)})})},p.prototype.skipWhile=function(r){r=m.createLambda(r);var o=this;return new p(function(){var t,e=0,n=!1;return new l(function(){t=o.getEnumerator()},function(){for(;!n;){if(!t.moveNext())return!1;if(!r(t.current(),e++))return n=!0,this.yieldReturn(t.current())}return!!t.moveNext()&&this.yieldReturn(t.current())},function(){m.dispose(t)})})},p.prototype.take=function(n){var r=this;return new p(function(){var t,e=0;return new l(function(){t=r.getEnumerator()},function(){return!!(e++<n&&t.moveNext())&&this.yieldReturn(t.current())},function(){m.dispose(t)})})},p.prototype.takeWhile=function(n){n=m.createLambda(n);var r=this;return new p(function(){var t,e=0;return new l(function(){t=r.getEnumerator()},function(){return!(!t.moveNext()||!n(t.current(),e++))&&this.yieldReturn(t.current())},function(){m.dispose(t)})})},p.prototype.takeExceptLast=function(n){null==n&&(n=1);var r=this;return new p(function(){if(n<=0)return r.getEnumerator();var t,e=[];return new l(function(){t=r.getEnumerator()},function(){for(;t.moveNext();){if(e.length==n)return e.push(t.current()),this.yieldReturn(e.shift());e.push(t.current())}return!1},function(){m.dispose(t)})})},p.prototype.takeFromLast=function(r){if(r<=0||null==r)return p.empty();var o=this;return new p(function(){var t,e,n=[];return new l(function(){t=o.getEnumerator()},function(){for(;t.moveNext();)n.length==r&&n.shift(),n.push(t.current());return null==e&&(e=p.from(n).getEnumerator()),!!e.moveNext()&&this.yieldReturn(e.current())},function(){m.dispose(e)})})},p.prototype.indexOf=function(n){var r=null;return typeof n===i?this.forEach(function(t,e){if(n(t,e))return r=e,!1}):this.forEach(function(t,e){if(t===n)return r=e,!1}),null!==r?r:-1},p.prototype.lastIndexOf=function(n){var r=-1;return typeof n===i?this.forEach(function(t,e){n(t,e)&&(r=e)}):this.forEach(function(t,e){t===n&&(r=e)}),r},p.prototype.cast=function(){return this},p.prototype.asEnumerable=function(){return p.from(this)},p.prototype.toArray=function(){var e=[];return this.forEach(function(t){e.push(t)}),e},p.prototype.toLookup=function(o,u,t){o=m.createLambda(o),u=m.createLambda(u),t=m.createLambda(t);var i=new L(t);return this.forEach(function(t){var e=o(t),n=u(t),r=i.get(e);r!==s?r.push(n):i.add(e,[n])}),new n(i)},p.prototype.toObject=function(e,n){e=m.createLambda(e),n=m.createLambda(n);var r={};return this.forEach(function(t){r[e(t)]=n(t)}),r},p.prototype.toDictionary=function(e,n,t){e=m.createLambda(e),n=m.createLambda(n),t=m.createLambda(t);var r=new L(t);return this.forEach(function(t){r.add(e(t),n(t))}),r},p.prototype.toJSONString=function(t,e){if(typeof JSON===r||null==JSON.stringify)throw new Error("toJSONString can't find JSON.stringify. This works native JSON support Browser or include json2.js");return JSON.stringify(this.toArray(),t,e)},p.prototype.toJoinedString=function(t,e){return null==t&&(t=""),null==e&&(e=h.Identity),this.select(e).toArray().join(t)},p.prototype.doAction=function(n){var r=this;return n=m.createLambda(n),new p(function(){var t,e=0;return new l(function(){t=r.getEnumerator()},function(){return!!t.moveNext()&&(n(t.current(),e++),this.yieldReturn(t.current()))},function(){m.dispose(t)})})},p.prototype.forEach=function(t){t=m.createLambda(t);var e=0,n=this.getEnumerator();try{for(;n.moveNext()&&!1!==t(n.current(),e++););}finally{m.dispose(n)}},p.prototype.write=function(e,n){null==e&&(e=""),n=m.createLambda(n);var r=!0;this.forEach(function(t){r?r=!1:document.write(e),document.write(n(t))})},p.prototype.writeLine=function(e){e=m.createLambda(e),this.forEach(function(t){document.writeln(e(t)+"<br />")})},p.prototype.force=function(){var t=this.getEnumerator();try{for(;t.moveNext(););}finally{m.dispose(t)}},p.prototype.letBind=function(e){e=m.createLambda(e);var n=this;return new p(function(){var t;return new l(function(){t=p.from(e(n)).getEnumerator()},function(){return!!t.moveNext()&&this.yieldReturn(t.current())},function(){m.dispose(t)})})},p.prototype.share=function(){var t,e=this,n=!1;return new E(function(){return new l(function(){null==t&&(t=e.getEnumerator())},function(){if(n)throw new Error("enumerator is disposed");return!!t.moveNext()&&this.yieldReturn(t.current())},h.Blank)},function(){n=!0,m.dispose(t)})},p.prototype.memoize=function(){var e,n,r=this,o=!1;return new E(function(){var t=-1;return new l(function(){null==n&&(n=r.getEnumerator(),e=[])},function(){if(o)throw new Error("enumerator is disposed");return t++,e.length<=t?!!n.moveNext()&&this.yieldReturn(e[t]=n.current()):this.yieldReturn(e[t])},h.Blank)},function(){o=!0,m.dispose(n),e=null})},m.hasNativeIteratorSupport()&&(p.prototype[Symbol.iterator]=function(){return{enumerator:this.getEnumerator(),next:function(){return this.enumerator.moveNext()?{done:!1,value:this.enumerator.current()}:{done:!0}}}}),p.prototype.catchError=function(e){e=m.createLambda(e);var n=this;return new p(function(){var t;return new l(function(){t=n.getEnumerator()},function(){try{return!!t.moveNext()&&this.yieldReturn(t.current())}catch(t){return e(t),!1}},function(){m.dispose(t)})})},p.prototype.finallyAction=function(e){e=m.createLambda(e);var n=this;return new p(function(){var t;return new l(function(){t=n.getEnumerator()},function(){return!!t.moveNext()&&this.yieldReturn(t.current())},function(){try{m.dispose(t)}finally{e()}})})},p.prototype.log=function(e){return e=m.createLambda(e),this.doAction(function(t){typeof console!==r&&console.log(e(t))})},p.prototype.trace=function(e,n){return null==e&&(e="Trace"),n=m.createLambda(n),this.doAction(function(t){typeof console!==r&&console.log(e,n(t))})};var w=function(t,e,n,r,o){this.source=t,this.keySelector=m.createLambda(e),this.descending=r,this.parent=o,n&&(this.comparer=m.createLambda(n))};w.prototype=new p,w.prototype.createOrderedEnumerable=function(t,e,n){return new w(this.source,t,e,n,this)},w.prototype.thenBy=function(t,e){return this.createOrderedEnumerable(t,e,!1)},w.prototype.thenByDescending=function(t,e){return this.createOrderedEnumerable(t,e,!0)},w.prototype.getEnumerator=function(){var r,o,t=this,e=0;return new l(function(){r=[],o=[],t.source.forEach(function(t,e){r.push(t),o.push(e)});var n=g.create(t,null);n.GenerateKeys(r),o.sort(function(t,e){return n.compare(t,e)})},function(){return e<o.length&&this.yieldReturn(r[o[e++]])},h.Blank)};var g=function(t,e,n,r){this.keySelector=t,this.descending=n,this.child=r,this.comparer=e,this.keys=null};g.create=function(t,e){var n=new g(t.keySelector,t.comparer,t.descending,e);return null!=t.parent?g.create(t.parent,n):n},g.prototype.GenerateKeys=function(t){for(var e=t.length,n=this.keySelector,r=new Array(e),o=0;o<e;o++)r[o]=n(t[o]);this.keys=r,null!=this.child&&this.child.GenerateKeys(t)},g.prototype.compare=function(t,e){var n=this.comparer?this.comparer(this.keys[t],this.keys[e]):m.compare(this.keys[t],this.keys[e]);return 0==n?null!=this.child?this.child.compare(t,e):m.compare(t,e):this.descending?-n:n};var E=function(t,e){this.dispose=e,p.call(this,t)};E.prototype=new p;var b=function(t){this.getSource=function(){return t}};b.prototype=new p,b.prototype.any=function(t){return null==t?0<this.getSource().length:p.prototype.any.apply(this,arguments)},b.prototype.count=function(t){return null==t?this.getSource().length:p.prototype.count.apply(this,arguments)},b.prototype.elementAt=function(t){var e=this.getSource();return 0<=t&&t<e.length?e[t]:p.prototype.elementAt.apply(this,arguments)},b.prototype.elementAtOrDefault=function(t,e){e===s&&(e=null);var n=this.getSource();return 0<=t&&t<n.length?n[t]:e},b.prototype.first=function(t){var e=this.getSource();return null==t&&0<e.length?e[0]:p.prototype.first.apply(this,arguments)},b.prototype.firstOrDefault=function(t,e){if(t!==s)return p.prototype.firstOrDefault.apply(this,arguments);e=t;var n=this.getSource();return 0<n.length?n[0]:e},b.prototype.last=function(t){var e=this.getSource();return null==t&&0<e.length?e[e.length-1]:p.prototype.last.apply(this,arguments)},b.prototype.lastOrDefault=function(t,e){if(t!==s)return p.prototype.lastOrDefault.apply(this,arguments);e=t;var n=this.getSource();return 0<n.length?n[n.length-1]:e},b.prototype.skip=function(e){var n=this.getSource();return new p(function(){var t;return new l(function(){t=e<0?0:e},function(){return t<n.length&&this.yieldReturn(n[t++])},h.Blank)})},b.prototype.takeExceptLast=function(t){return null==t&&(t=1),this.take(this.getSource().length-t)},b.prototype.takeFromLast=function(t){return this.skip(this.getSource().length-t)},b.prototype.reverse=function(){var e=this.getSource();return new p(function(){var t;return new l(function(){t=e.length},function(){return 0<t&&this.yieldReturn(e[--t])},h.Blank)})},b.prototype.sequenceEqual=function(t,e){return(!(t instanceof b||t instanceof Array)||null!=e||p.from(t).count()==this.count())&&p.prototype.sequenceEqual.apply(this,arguments)},b.prototype.toJoinedString=function(t,e){var n=this.getSource();return null==e&&n instanceof Array?(null==t&&(t=""),n.join(t)):p.prototype.toJoinedString.apply(this,arguments)},b.prototype.getEnumerator=function(){var t=this.getSource(),e=-1;return{current:function(){return t[e]},moveNext:function(){return++e<t.length},dispose:h.Blank}};var k=function(t,e){this.prevSource=t,this.prevPredicate=e};k.prototype=new p,k.prototype.where=function(e){if((e=m.createLambda(e)).length<=1){var n=this.prevPredicate;return new k(this.prevSource,function(t){return n(t)&&e(t)})}return p.prototype.where.call(this,e)},k.prototype.select=function(t){return(t=m.createLambda(t)).length<=1?new x(this.prevSource,this.prevPredicate,t):p.prototype.select.call(this,t)},k.prototype.getEnumerator=function(){var t,e=this.prevPredicate,n=this.prevSource;return new l(function(){t=n.getEnumerator()},function(){for(;t.moveNext();)if(e(t.current()))return this.yieldReturn(t.current());return!1},function(){m.dispose(t)})};var x=function(t,e,n){this.prevSource=t,this.prevPredicate=e,this.prevSelector=n};x.prototype=new p,x.prototype.where=function(t){return(t=m.createLambda(t)).length<=1?new k(this,t):p.prototype.where.call(this,t)},x.prototype.select=function(e){if((e=m.createLambda(e)).length<=1){var n=this.prevSelector;return new x(this.prevSource,this.prevPredicate,function(t){return e(n(t))})}return p.prototype.select.call(this,e)},x.prototype.getEnumerator=function(){var t,e=this.prevPredicate,n=this.prevSelector,r=this.prevSource;return new l(function(){t=r.getEnumerator()},function(){for(;t.moveNext();)if(null==e||e(t.current()))return this.yieldReturn(n(t.current()));return!1},function(){m.dispose(t)})};var L=function(){function a(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function c(t){return null===t?"null":t===s?"undefined":typeof t.toString===i?t.toString():Object.prototype.toString.call(t)}function f(t,e){this.key=t,this.value=e,this.prev=null,this.next=null}function e(){this.first=null,this.last=null}e.prototype={addLast:function(t){null!=this.last?((this.last.next=t).prev=this.last,this.last=t):this.first=this.last=t},replace:function(t,e){null!=t.prev?(t.prev.next=e).prev=t.prev:this.first=e,null!=t.next?(t.next.prev=e).next=t.next:this.last=e},remove:function(t){null!=t.prev?t.prev.next=t.next:this.first=t.next,null!=t.next?t.next.prev=t.prev:this.last=t.prev}};function t(t){this.countField=0,this.entryList=new e,this.buckets={},this.compareSelector=null==t?h.Identity:t}return t.prototype={add:function(t,e){var n=this.compareSelector(t),r=c(n),o=new f(t,e);if(a(this.buckets,r)){for(var u=this.buckets[r],i=0;i<u.length;i++)if(this.compareSelector(u[i].key)===n)return this.entryList.replace(u[i],o),void(u[i]=o);u.push(o)}else this.buckets[r]=[o];this.countField++,this.entryList.addLast(o)},get:function(t){var e=this.compareSelector(t),n=c(e);if(!a(this.buckets,n))return s;for(var r=this.buckets[n],o=0;o<r.length;o++){var u=r[o];if(this.compareSelector(u.key)===e)return u.value}return s},set:function(t,e){var n=this.compareSelector(t),r=c(n);if(a(this.buckets,r))for(var o=this.buckets[r],u=0;u<o.length;u++)if(this.compareSelector(o[u].key)===n){var i=new f(t,e);return this.entryList.replace(o[u],i),o[u]=i,!0}return!1},contains:function(t){var e=this.compareSelector(t),n=c(e);if(!a(this.buckets,n))return!1;for(var r=this.buckets[n],o=0;o<r.length;o++)if(this.compareSelector(r[o].key)===e)return!0;return!1},clear:function(){this.countField=0,this.buckets={},this.entryList=new e},remove:function(t){var e=this.compareSelector(t),n=c(e);if(a(this.buckets,n))for(var r=this.buckets[n],o=0;o<r.length;o++)if(this.compareSelector(r[o].key)===e)return this.entryList.remove(r[o]),r.splice(o,1),0==r.length&&delete this.buckets[n],void this.countField--},count:function(){return this.countField},toEnumerable:function(){var t=this;return new p(function(){var e;return new l(function(){e=t.entryList.first},function(){if(null==e)return!1;var t={key:e.key,value:e.value};return e=e.next,this.yieldReturn(t)},h.Blank)})}},t}(),n=function(e){this.count=function(){return e.count()},this.get=function(t){return p.from(e.get(t))},this.contains=function(t){return e.contains(t)},this.toEnumerable=function(){return e.toEnumerable().select(function(t){return new N(t.key,t.value)})}},N=function(t,e){this.key=function(){return t},b.call(this,e)};N.prototype=new b,typeof define===i&&define.amd?define("linqjs",[],function(){return p}):typeof module!==r&&module.exports?module.exports=p:t.Enumerable=p}(this);