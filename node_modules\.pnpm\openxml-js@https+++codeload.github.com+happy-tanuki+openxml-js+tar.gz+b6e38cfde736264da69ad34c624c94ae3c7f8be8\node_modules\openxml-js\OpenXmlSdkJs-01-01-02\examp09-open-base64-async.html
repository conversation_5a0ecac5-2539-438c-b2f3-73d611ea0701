﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Shows loading and saving a base64 document asynchronously</title>
</head>
<body>
<form>
    <div style="margin-top: 1em; margin-bottom: 1em;">
        <input id="btnSave" type="button" onclick="saveDocument()" value="Save" style="height: 30px; width: 100px; background-color: rgb(212,208,200); border: thin solid black;"  />
    </div>
    <p>If you are targeting down-level browsers that do not support HTML5, see <b>examp01-load-save-via-flash.html</b>.</p>
</form>
<pre>
<script type="text/javascript" src="linq.js"></script>
<script type="text/javascript" src="ltxml.js"></script>
<script type="text/javascript" src="ltxml-extensions.js"></script>
<script type="text/javascript" src="jszip.js"></script>
<script type="text/javascript" src="jszip-load.js"></script>
<script type="text/javascript" src="jszip-inflate.js"></script>
<script type="text/javascript" src="jszip-deflate.js"></script>
<script type="text/javascript" src="FileSaver.js"></script>
<script type="text/javascript" src="openxml.js"></script>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript">
    /***************************************************************************
    
    Copyright (c) Microsoft Corporation 2013.
    
    This code is licensed using the Microsoft Public License (Ms-PL).  You can find the text of the license here:
    
    http://www.microsoft.com/resources/sharedsource/licensingbasics/publiclicense.mspx
    
    Published at http://OpenXmlDeveloper.org
    Resource Center and Documentation: http://openxmldeveloper.org/wiki/w/wiki/open-xml-sdk-for-javascript.aspx
    
    Developer: Eric White
    Blog: http://www.ericwhite.com
    Twitter: @EricWhiteDev
	Email: <EMAIL>
    
    ***************************************************************************/

    var saveDocument;
    var documentToSave;

    (function (root) {  // root = global
        "use strict";

        var XAttribute = Ltxml.XAttribute;
        var XCData = Ltxml.XCData;
        var XComment = Ltxml.XComment;
        var XContainer = Ltxml.XContainer;
        var XDeclaration = Ltxml.XDeclaration;
        var XDocument = Ltxml.XDocument;
        var XElement = Ltxml.XElement;
        var XName = Ltxml.XName;
        var XNamespace = Ltxml.XNamespace;
        var XNode = Ltxml.XNode;
        var XObject = Ltxml.XObject;
        var XProcessingInstruction = Ltxml.XProcessingInstruction;
        var XText = Ltxml.XText;
        var XEntity = Ltxml.XEntity;
        var cast = Ltxml.cast;
        var castInt = Ltxml.castInt;

        var W = openXml.W;
        var NN = openXml.NoNamespace;
        var wNs = openXml.wNs;

        var templateDocument = "UEsDBBQABgAIAAAAIQDfpNJsWgEAACAFAAATAAgCW0NvbnRlbnRfVHlwZXNdLnhtbCCiBAIooAAC" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC0" +
"lMtuwjAQRfeV+g+Rt1Vi6KKqKgKLPpYtUukHGHsCVv2Sx7z+vhMCUVUBkQpsIiUz994zVsaD0dqa" +
"bAkRtXcl6xc9loGTXmk3K9nX5C1/ZBkm4ZQw3kHJNoBsNLy9GUw2ATAjtcOSzVMKT5yjnIMVWPgA" +
"jiqVj1Ykeo0zHoT8FjPg973eA5feJXApT7UHGw5eoBILk7LXNX1uSCIYZNlz01hnlUyEYLQUiep8" +
"6dSflHyXUJBy24NzHfCOGhg/mFBXjgfsdB90NFEryMYipndhqYuvfFRcebmwpCxO2xzg9FWlJbT6" +
"2i1ELwGRztyaoq1Yod2e/ygHpo0BvDxF49sdDymR4BoAO+dOhBVMP69G8cu8E6Si3ImYGrg8Rmvd" +
"CZFoA6F59s/m2NqciqTOcfQBaaPjP8ber2ytzmngADHp039dm0jWZ88H9W2gQB3I5tv7bfgDAAD/" +
"/wMAUEsDBBQABgAIAAAAIQAekRq37wAAAE4CAAALAAgCX3JlbHMvLnJlbHMgogQCKKAAAgAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAArJLBasMw" +
"DEDvg/2D0b1R2sEYo04vY9DbGNkHCFtJTBPb2GrX/v082NgCXelhR8vS05PQenOcRnXglF3wGpZV" +
"DYq9Cdb5XsNb+7x4AJWFvKUxeNZw4gyb5vZm/cojSSnKg4tZFYrPGgaR+IiYzcAT5SpE9uWnC2ki" +
"Kc/UYySzo55xVdf3mH4zoJkx1dZqSFt7B6o9Rb6GHbrOGX4KZj+xlzMtkI/C3rJdxFTqk7gyjWop" +
"9SwabDAvJZyRYqwKGvC80ep6o7+nxYmFLAmhCYkv+3xmXBJa/ueK5hk/Nu8hWbRf4W8bnF1B8wEA" +
"AP//AwBQSwMEFAAGAAgAAAAhANZks1H0AAAAMQMAABwACAF3b3JkL19yZWxzL2RvY3VtZW50Lnht" +
"bC5yZWxzIKIEASigAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAArJLLasMwEEX3hf6DmH0t" +
"O31QQuRsSiHb1v0ARR4/qCwJzfThv69ISevQYLrwcq6Yc8+ANtvPwYp3jNR7p6DIchDojK971yp4" +
"qR6v7kEQa1dr6x0qGJFgW15ebJ7Qak5L1PWBRKI4UtAxh7WUZDocNGU+oEsvjY+D5jTGVgZtXnWL" +
"cpXndzJOGVCeMMWuVhB39TWIagz4H7Zvmt7ggzdvAzo+UyE/cP+MzOk4SlgdW2QFkzBLRJDnRVZL" +
"itAfi2Myp1AsqsCjxanAYZ6rv12yntMu/rYfxu+wmHO4WdKh8Y4rvbcTj5/oKCFPPnr5BQAA//8D" +
"AFBLAwQUAAYACAAAACEAKuHNzUwCAACRBgAAEQAAAHdvcmQvZG9jdW1lbnQueG1spFXbbuIwEH1f" +
"af8h8ntJQlNKo4ZKu1DEw0rVtvu8Mo6TWMQeyzZQ9ut3nAthL6poiaLYcztzxoOH+4dXWQc7bqwA" +
"lZF4FJGAKwa5UGVGfrw8Xk1JYB1VOa1B8YwcuCUPs8+f7vdpDmwruXIBQiib7jXLSOWcTsPQsopL" +
"akdSMAMWCjdiIEMoCsF4uAeTh+MojpqdNsC4tZjvK1U7akkHJ/9FA80VGgswkjoUTRlKajZbfYXo" +
"mjqxFrVwB8SOJj0MZGRrVNpBXB0J+ZC0JdQtfYQ5J28bMu9OoMkYGl4jB1C2Enoo46NoaKx6kN1b" +
"Rexk3fvtdZxc1oO5oXtcBsBz6OdtkKxb5m8jxtEZHfEQx4hzKPyZs2ciqVBD4g8dzcnhxjfvAxj/" +
"DaDLy5qzNLDVA5q4DG2lNkcsf7PfgdU1+bQ0exmZ54pqvIGSpatSgaHrGhlhywI89cD/rMkMJ84a" +
"8oNfNaqTVFNDV3lGJtF1sriZ4uTyWsdfndfedg9qU5xu+feMRNFjFM/ju6Nqzgu6rZ23LJLx3WLe" +
"ZDH+42YvlbABvjRwXGq82jzoJ97oPvQe/ts4rwE2fhI9O2ocggvM7+mkikqs4+cSvlC2IeGp70Ll" +
"R8+wgdLebDlzT+Y/nJu6y+dfaMIbEY/HSZOhwv3NNGkwvMM36oMd4MWNk9bFiLJyg7gG50AOcs2L" +
"E2vFac5xBN6OG7EAcCdiuXWN2KVjUFvUWk0Zb30aNR7T0ghfXi0UfxKOIcvrSV9nW2KzbRsaDv8l" +
"s98AAAD//wMAUEsDBBQABgAIAAAAIQCqUiXfIwYAAIsaAAAVAAAAd29yZC90aGVtZS90aGVtZTEu" +
"eG1s7FlNixs3GL4X+h/E3B1/zfhjiTfYYztps5uE7CYlR3lGnlGsGRlJ3l0TAiU5Fgqlaemhgd56" +
"KG0DCfSS/pptU9oU8heq0XhsyZZZ2mxgKVnDWh/P++rR+0qPNJ7LV04SAo4Q45imHad6qeIAlAY0" +
"xGnUce4cDkstB3AB0xASmqKOM0fcubL74QeX4Y6IUYKAtE/5Duw4sRDTnXKZB7IZ8kt0ilLZN6Ys" +
"gUJWWVQOGTyWfhNSrlUqjXICceqAFCbS7c3xGAcIHGYund3C+YDIf6ngWUNA2EHmGhkWChtOqtkX" +
"n3OfMHAESceR44T0+BCdCAcQyIXs6DgV9eeUdy+Xl0ZEbLHV7Ibqb2G3MAgnNWXHotHS0HU9t9Fd" +
"+lcAIjZxg+agMWgs/SkADAI505yLjvV67V7fW2A1UF60+O43+/Wqgdf81zfwXS/7GHgFyovuBn44" +
"9Fcx1EB50bPEpFnzXQOvQHmxsYFvVrp9t2ngFSgmOJ1soCteo+4Xs11CxpRcs8Lbnjts1hbwFaqs" +
"ra7cPhXb1loC71M2lACVXChwCsR8isYwkDgfEjxiGOzhKJYLbwpTymVzpVYZVuryf/ZxVUlFBO4g" +
"qFnnTQHfaMr4AB4wPBUd52Pp1dEgb17++Oblc3D66MXpo19OHz8+ffSzxeoaTCPd6vX3X/z99FPw" +
"1/PvXj/5yo7nOv73nz777dcv7UChA199/eyPF89effP5nz88scC7DI50+CFOEAc30DG4TRM5McsA" +
"aMT+ncVhDLFu0U0jDlOY2VjQAxEb6BtzSKAF10NmBO8yKRM24NXZfYPwQcxmAluA1+PEAO5TSnqU" +
"Wed0PRtLj8IsjeyDs5mOuw3hkW1sfy2/g9lUrndsc+nHyKB5i8iUwwilSICsj04Qspjdw9iI6z4O" +
"GOV0LMA9DHoQW0NyiEfGaloZXcOJzMvcRlDm24jN/l3Qo8Tmvo+OTKTcFZDYXCJihPEqnAmYWBnD" +
"hOjIPShiG8mDOQuMgHMhMx0hQsEgRJzbbG6yuUH3upQXe9r3yTwxkUzgiQ25BynVkX068WOYTK2c" +
"cRrr2I/4RC5RCG5RYSVBzR2S1WUeYLo13XcxMtJ99t6+I5XVvkCynhmzbQlEzf04J2OIlPPymp4n" +
"OD1T3Ndk3Xu3si6F9NW3T+26eyEFvcuwdUety/g23Lp4+5SF+OJrdx/O0ltIbhcL9L10v5fu/710" +
"b9vP5y/YK41Wl/jiqq7cJFvv7WNMyIGYE7THlbpzOb1wKBtVRRktHxOmsSwuhjNwEYOqDBgVn2AR" +
"H8RwKoepqhEivnAdcTClXJ4PqtnqO+sgs2SfhnlrtVo8mUoDKFbt8nwp2uVpJPLWRnP1CLZ0r2qR" +
"elQuCGS2/4aENphJom4h0SwazyChZnYuLNoWFq3M/VYW6muRFbn/AMx+1PDcnJFcb5CgMMtTbl9k" +
"99wzvS2Y5rRrlum1M67nk2mDhLbcTBLaMoxhiNabzznX7VVKDXpZKDZpNFvvIteZiKxpA0nNGjiW" +
"e67uSTcBnHacsbwZymIylf54ppuQRGnHCcQi0P9FWaaMiz7kcQ5TXfn8EywQAwQncq3raSDpilu1" +
"1szmeEHJtSsXL3LqS08yGo9RILa0rKqyL3di7X1LcFahM0n6IA6PwYjM2G0oA+U1q1kAQ8zFMpoh" +
"ZtriXkVxTa4WW9H4xWy1RSGZxnBxouhinsNVeUlHm4diuj4rs76YzCjKkvTWp+7ZRlmHJppbDpDs" +
"1LTrx7s75DVWK903WOXSva517ULrtp0Sb38gaNRWgxnUMsYWaqtWk9o5Xgi04ZZLc9sZcd6nwfqq" +
"zQ6I4l6pahuvJujovlz5fXldnRHBFVV0Ip8R/OJH5VwJVGuhLicCzBjuOA8qXtf1a55fqrS8Qcmt" +
"u5VSy+vWS13Pq1cHXrXS79UeyqCIOKl6+dhD+TxD5os3L6p94+1LUlyzLwU0KVN1Dy4rY/X2pVrb" +
"/vYFYBmZB43asF1v9xqldr07LLn9XqvU9hu9Ur/hN/vDvu+12sOHDjhSYLdb993GoFVqVH2/5DYq" +
"Gf1Wu9R0a7Wu2+y2Bm734SLWcubFdxFexWv3HwAAAP//AwBQSwMEFAAGAAgAAAAhAJ9R6t6lAwAA" +
"sgkAABEAAAB3b3JkL3NldHRpbmdzLnhtbLRWbY/aOBD+ftL9B5TPlyWBwLJR2eqA5brVcq0a+gOc" +
"2AFr/SbbgaWn++83duINu60qelU/MZln3jx+Zsybt0+cDQ5EGyrFPEqvkmhARCUxFbt59Hm7jmfR" +
"wFgkMGJSkHl0IiZ6e/v7b2+OuSHWgpkZQAhhcl7No721Kh8OTbUnHJkrqYgAsJaaIwufejfkSD82" +
"Kq4kV8jSkjJqT8NRkkyjLoycR40WeRci5rTS0sjaOpdc1jWtSPcTPPQleVuXlawaToT1GYeaMKhB" +
"CrOnyoRo/P9GA3Afghy+d4gDZ8HumCYXHPcoNX72uKQ856C0rIgxcEGchQKp6BNnXwV6zn0Fubsj" +
"+lDgniZeOq988mMBRq8CGHbJSVrogZYa6ZYn3TF4ld/vhNSoZMBKOM4AKopugZZfpOSDY66IruBu" +
"gNNJEg0dgEmNGma3qCysVGByQFDD9aiDqz3SqLJEFwpV0LalFFZLFuyw/FvaJdBWQ1c7D0/iXira" +
"gQAPgThU9YLkG4mBsce80fTyxjkHnx3OdpbydSIJA6wpJlvXjcKeGFlD8QX9Qv4U+H1jLIWInuo/" +
"UcH3CiDCZf4A97c9KbImyDbQpl+UzN/EmlG1oVpLfS8w3PMvS0brmmhIQJElG6AP1fLo+/yOIAx7" +
"8yfzDs9pBFsYmyB8ktIG0yRJ1+P1eNZW6tBLkLtsdHO3+hayTtJVetPl77Ly3G2wjzpIjkID3nos" +
"ES81RYON23FDZ1HqxwUVAS8JDC05R4qmDGAct4DhiLE1zFgA/ODxHFOjVqT2Mtsgvevjdhb6m1qY" +
"5/fPsdysE/2Xlo1q0aNGqqVGMEmzrPOkwj5QHvSmKYvgJWDNnEGNwB8O2vepb88xt3DFfsQekKeK" +
"tyUi/lx0VGK6cDQgG6RUy6Zyl84jRnd7mzoCWPjC8BT6j3I36rCRx0Yt5j9Q5U4G1p3Q60ZBd2Y3" +
"Drpxr8uCLut1k6Cb9Lpp0E2dbg9zrBkVj0DsIDp9LRmTR4Lf9fhXqrYJZo8UWbU7F+glW0W3hM3g" +
"kJMn2M4EUwv/MBTFHD25ZT2aOvfOmqGTbOwLW4c5Y/UyAkYWhZF64ewp/qoW9xZUFOhYnHjZr/ir" +
"tnBGDawBBa+BlTpgf3gszXIsq3uYJJC8PpssZ9PJzBcNL5B/RewWSP4I9/6J1AtkCO6w4DppXf+Z" +
"LLLFZJYs4tVyvIyzu7txPLterOPVzfR6OkkW6eQ6/bcb0vBn6/Y/AAAA//8DAFBLAwQUAAYACAAA" +
"ACEA9qZ55cQBAADtBAAAEgAAAHdvcmQvZm9udFRhYmxlLnhtbLySbWvbMBDH3w/6HYzeN5adpA+m" +
"TsmyBgZjL0b3ARRFtkX1YHRK3Hz7nmTHGwtlCYXKIOT/3f10+nMPj69aJXvhQFpTkmxCSSIMt1tp" +
"6pL8fl5f35EEPDNbpqwRJTkIII+Lqy8PXVFZ4yHBegOF5iVpvG+LNAXeCM1gYlthMFhZp5nHX1en" +
"mrmXXXvNrW6ZlxuppD+kOaU3ZMC4cyi2qiQX3yzfaWF8rE+dUEi0BhrZwpHWnUPrrNu2znIBgG/W" +
"qudpJs2IyWYnIC25s2ArP8HHDB1FFJZnNJ60+gOYXwbIR4DmxffaWMc2Cs3HThKEkcXgftIVhmkM" +
"rJiSGydjoGXGgsgwtmeqJDSnazrHPXwzOg07SUMib5gDESB9Iu3limmpDkcVOgnQB1rpeXPU98zJ" +
"0FQfAlljYAcbWpIniitfr0mvZCWZobBcjUoe7oorG5TpqNCg8MjpM+5jFY+cMQfvTHsHTpx4llpA" +
"8lN0yS+rmXnHkZzeoBNz9CM4M73IERe5lzqSL/92ZIXK7d1seuLI/f8d6TnnOzLMRvJD1o1/d0LC" +
"XHzWhCxDy/nTPxOS09uvJ37E139wQoYDLN4AAAD//wMAUEsDBBQABgAIAAAAIQBbbf2TCQEAAPEB" +
"AAAUAAAAd29yZC93ZWJTZXR0aW5ncy54bWyU0cFKAzEQBuC74DssubfZFhVZui2IVLyIoD5Ams62" +
"wUwmzKSu9ekda61IL/WWSTIfM/yT2TvG6g1YAqXWjIa1qSB5Woa0as3L83xwbSopLi1dpASt2YKY" +
"2fT8bNI3PSyeoBT9KZUqSRr0rVmXkhtrxa8BnQwpQ9LHjhhd0ZJXFh2/bvLAE2ZXwiLEULZ2XNdX" +
"Zs/wKQp1XfBwS36DkMqu3zJEFSnJOmT50fpTtJ54mZk8iOg+GL89dCEdmNHFEYTBMwl1ZajL7Cfa" +
"Udo+qncnjL/A5f+A8QFA39yvErFbRI1AJ6kUM1PNgHIJGD5gTnzD1Auw/bp2MVL/+HCnhf0T1PQT" +
"AAD//wMAUEsDBBQABgAIAAAAIQDrGxI5cgEAAMcCAAAQAAgBZG9jUHJvcHMvYXBwLnhtbCCiBAEo" +
"oAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJxSy07DMBC8I/EPUe7UaQUIoY0RaoU48JKa" +
"lrNlbxILx7Zst6J/z4a0IYgbPu3Mekcza8PdZ2eyPYaonS3z+azIM7TSKW2bMt9UDxc3eRaTsEoY" +
"Z7HMDxjzO35+Bm/BeQxJY8xIwsYyb1Pyt4xF2WIn4ozaljq1C51IBEPDXF1riSsndx3axBZFcc3w" +
"M6FVqC78KJgPirf79F9R5WTvL26rgyc9DhV23oiE/KWfNDPlUgdsZKFySZhKd8gXRI8A3kSDkc+B" +
"DQW8u6AivwQ2FLBsRRAy0f744grYBMK990ZLkWix/FnL4KKrU/b67Tbrx4FNrwAlWKPcBZ0OvAA2" +
"hfCk7WBjKMhWEE0Qvj16GxGspTC4pOy8FiYisB8Clq7zwpIcGyvS+4gbX7lVv4bjyG9ykvFdp3bt" +
"hSQLi5tp2kkD1sSiIvujg5GAR3qOYHp5mrUNqtOdv41+f9vhX/L51ayg872wE0exxw/DvwAAAP//" +
"AwBQSwMEFAAGAAgAAAAhAKqkQahvAQAA6wIAABEACAFkb2NQcm9wcy9jb3JlLnhtbCCiBAEooAAB" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIySXU/CMBSG7038D0vvRzdQYpZtJH5wJYmJGIx3" +
"tT1AZe2a9sDk39ttbLjIhXfn4zlvT982nX2rIjiAdbLUGYlHEQlA81JIvcnI23Ie3pHAIdOCFaWG" +
"jBzBkVl+fZVyk/DSwostDViU4AKvpF3CTUa2iCah1PEtKOZGntC+uS6tYuhTu6GG8R3bAB1H0ZQq" +
"QCYYMloLhqZXJCdJwXtJs7dFIyA4hQIUaHQ0HsX0zCJY5S4ONJ1fpJJ4NHAR7Zo9/e1kD1ZVNaom" +
"Der3j+n74vm1uWoode0VB5KngicosYA8pefQR27/+QUc23Kf+JhbYFja/MlKHqy2EqFBunJt+A6O" +
"VWmF88ODzGMCHLfSoH/GVnpQ8HTBHC78u64liPvj4JS/3XrAwkHWvyIfN0SfpieL281ABN6apDWy" +
"66wmD4/LOcnHUTwJo2k4vl1G0yS+SaLoo15uMH8WVKcF/q84HSp2Aq0/w++Z/wAAAP//AwBQSwME" +
"FAAGAAgAAAAhAAtGahAbCwAABHAAAA8AAAB3b3JkL3N0eWxlcy54bWy8nV1z27oRhu870//A0VV7" +
"kcjyZ+I5zhnbiWtP4xyfyGmuIRKyUIOEyo/Y7q8vAFIS5CUoLrj1lS1R+wDEuy+I5Yf02+/PqYx+" +
"8bwQKjsbTd7vjSKexSoR2cPZ6Mf91bsPo6goWZYwqTJ+Nnrhxej3T3/9y29Pp0X5InkRaUBWnKbx" +
"2WhRlsvT8biIFzxlxXu15JneOFd5ykr9Mn8Ypyx/rJbvYpUuWSlmQoryZby/t3c8ajB5H4qaz0XM" +
"P6u4SnlW2vhxzqUmqqxYiGWxoj31oT2pPFnmKuZFoXc6lTUvZSJbYyaHAJSKOFeFmpfv9c40PbIo" +
"HT7Zs/+lcgM4wgH214A0Pr15yFTOZlKPvu5JpGGjT3r4ExV/5nNWybIwL/O7vHnZvLJ/rlRWFtHT" +
"KStiIe51yxqSCs27Ps8KMdJbOCvK80Kw1o0L80/rlrgonbcvRCJGY9Ni8V+98ReTZ6P9/dU7l6YH" +
"W+9Jlj2s3uPZux9TtyfOWzPNPRux/N303ASOmx2r/zq7u3z9yja8ZLGw7bB5yXVmTY73DFQKk8j7" +
"Rx9XL75XZmxZVaqmEQuo/66xYzDiOuF0+k1rF+itfP5VxY88mZZ6w9nItqXf/HFzlwuV60w/G320" +
"beo3pzwV1yJJeOZ8MFuIhP9c8OxHwZPN+39e2Wxt3ohVlen/D04mNgtkkXx5jvnS5L7emjGjyTcT" +
"IM2nK7Fp3Ib/ZwWbNEq0xS84MxNANHmNsN1HIfZNROHsbTuzerXv9lOohg7eqqHDt2ro6K0aOn6r" +
"hk7eqqEPb9WQxfw/GxJZwp9rI8JmAHUXx+NGNMdjNjTH4yU0x2MVNMfjBDTHk+hojieP0RxPmiI4" +
"pYp9Wegk+4En27u5u48RYdzdh4Qw7u4jQBh394Qfxt09v4dxd0/nYdzds3cYd/dkjefWS63oRtss" +
"Kwe7bK5UmamSRyV/Hk5jmWbZqoiGZw56PCfZSQJMPbM1B+LBtJjZ17szxJo0/HhemkIuUvNoLh6q" +
"XBfTQzvOs19c6rI2YkmieYTAnJdV7hmRkJzO+ZznPIs5ZWLTQU0lGGVVOiPIzSV7IGPxLCEevhWR" +
"ZFJYJ7SunxfGJIIgqVMW52p41xQjmx++imL4WBlIdFFJyYlY32hSzLKG1wYWM7w0sJjhlYHFDC8M" +
"HM2ohqihEY1UQyMasIZGNG51flKNW0MjGreGRjRuDW34uN2LUtop3l11TPqfu7uUypzHHtyPqXjI" +
"mF4ADD/cNOdMozuWs4ecLReROSvdjnX3GdvOhUpeonuKY9qaRLWutylyqfdaZNXwAd2iUZlrzSOy" +
"15pHZLA1b7jFbvUy2SzQrmnqmWk1K1tNa0m9TDtlsqoXtMPdxsrhGbYxwJXICzIbtGMJMvibWc4a" +
"OSlmvk0vh3dswxpuq9ezEmn3GiRBL6WKH2mm4euXJc91WfY4mHSlpFRPPKEjTstc1bnmWn7fStLL" +
"8l/S5YIVwtZKW4j+h/rVFfDoli0H79CdZCKj0e3Lu5QJGdGtIK7vb79G92ppykwzMDTAC1WWKiVj" +
"NmcC//aTz/5O08FzXQRnL0R7e050esjCLgXBQaYmqYSIpJeZIhMkx1DL+yd/mSmWJzS0u5zXN52U" +
"nIg4ZemyXnQQeEvPi096/iFYDVnev1guzHkhKlPdk8Cc04ZFNfs3j4dPdd9URHJm6I+qtOcf7VLX" +
"RtPhhi8TtnDDlwhWTX14MPlLsLNbuOE7u4Wj2tlLyYpCeC+hBvOodnfFo97f4cVfw1NS5fNK0g3g" +
"Ckg2gisg2RAqWaVZQbnHlke4w5ZHvb+EKWN5BKfkLO8fuUjIxLAwKiUsjEoGC6PSwMJIBRh+h44D" +
"G36bjgMbfq9ODSNaAjgwqjwjPfwTXeVxYFR5ZmFUeWZhVHlmYVR5dvA54vO5XgTTHWIcJFXOOUi6" +
"A01W8nSpcpa/ECG/SP7ACE6Q1rS7XM3N0wgqq2/iJkCac9SScLFd46hE/slnZF0zLMp+EZwRZVIq" +
"RXRubXPAsZHb967tCrNPcgzuwp1kMV8omfDcs0/+WF0vT+vHMl5333aj12nPr+JhUUbTxfpsv4s5" +
"3tsZuSrYt8J2N9g25ser51nawm55Iqp01VH4MMXxQf9gm9FbwYe7gzcria3Io56RsM3j3ZGbVfJW" +
"5EnPSNjmh56R1qdbkV1++Mzyx9ZEOOnKn3WN50m+k64sWge3NtuVSOvIthQ86cqiLatE53FsrhZA" +
"dfp5xh/fzzz+eIyL/BSMnfyU3r7yI7oM9p3/EubIjpk0bXvruyfAvG8X0b1mzj8rVZ+337rg1P+h" +
"rhu9cMoKHrVyDvpfuNqaZfzj2Hu68SN6zzt+RO8JyI/oNRN5w1FTkp/Se27yI3pPUn4EeraCRwTc" +
"bAXjcbMVjA+ZrSAlZLYasArwI3ovB/wItFEhAm3UASsFPwJlVBAeZFRIQRsVItBGhQi0UeECDGdU" +
"GI8zKowPMSqkhBgVUtBGhQi0USECbVSIQBsVItBGDVzbe8ODjAopaKNCBNqoEIE2ql0vDjAqjMcZ" +
"FcaHGBVSQowKKWijQgTaqBCBNipEoI0KEWijQgTKqCA8yKiQgjYqRKCNChFoo9aPGoYbFcbjjArj" +
"Q4wKKSFGhRS0USECbVSIQBsVItBGhQi0USECZVQQHmRUSEEbFSLQRoUItFHtxcIBRoXxOKPC+BCj" +
"QkqIUSEFbVSIQBsVItBGhQi0USECbVSIQBkVhAcZFVLQRoUItFEhois/m0uUvtvsJ/iznt479vtf" +
"umo69d19lNtFHfRHrXrlZ/V/FuFCqceo9cHDA1tv9IOImRTKnqL2XFZ3ufaWCNSFzz8uu5/wcekD" +
"v3SpeRbCXjMF8MO+keCcymFXyruRoMg77Mp0NxKsOg+7Zl83EhwGD7smXevL1U0p+nAEgrumGSd4" +
"4gnvmq2dcDjEXXO0EwhHuGtmdgLhAHfNx07gUWQm59fRRz3H6Xh9fykgdKWjQzjxE7rSEmq1mo6h" +
"MfqK5if0Vc9P6Cujn4DS04vBC+tHoRX2o8KkhjbDSh1uVD8BKzUkBEkNMOFSQ1Sw1BAVJjWcGLFS" +
"QwJW6vDJ2U8IkhpgwqWGqGCpISpMangow0oNCVipIQEr9cADshcTLjVEBUsNUWFSw8UdVmpIwEoN" +
"CVipISFIaoAJlxqigqWGqDCpQZWMlhoSsFJDAlZqSAiSGmDCpYaoYKkhqktqexZlS2qUwk44bhHm" +
"BOIOyE4gbnJ2AgOqJSc6sFpyCIHVEtRqpTmuWnJF8xP6qucn9JXRT0Dp6cXghfWj0Ar7UWFS46ql" +
"NqnDjeonYKXGVUteqXHVUqfUuGqpU2pcteSXGlcttUmNq5bapA6fnP2EIKlx1VKn1LhqqVNqXLXk" +
"lxpXLbVJjauW2qTGVUttUg88IHsx4VLjqqVOqXHVkl9qXLXUJjWuWmqTGlcttUmNq5a8UuOqpU6p" +
"cdVSp9S4askvNa5aapMaVy21SY2rltqkxlVLXqlx1VKn1LhqqVNqT7U0ftr6ASbDtj9Ipj9cviy5" +
"+Q5u54GZpP4O0uYioP3gTbL+oSQTbHoSNT9J1bxtO9xcMKxbtIGwqXih24qbb0/yNNV8C+r6MR77" +
"HaivG/Z8VartyGYIVp9uhnRzKbT+3NZlz85+l2bIO/psJekco1o1Xwc/Nmm4q4e6PzNZ/2iX/ucm" +
"SzTgqfnBqrqnyTOrUXr7JZfyltWfVkv/RyWfl/XWyZ59aP7V9ln9/W/e+NxOFF7AeLsz9cvmh8M8" +
"411/I3xzBdubksYNLcNtb6cYOtKbvq3+Kz79DwAA//8DAFBLAQItABQABgAIAAAAIQDfpNJsWgEA" +
"ACAFAAATAAAAAAAAAAAAAAAAAAAAAABbQ29udGVudF9UeXBlc10ueG1sUEsBAi0AFAAGAAgAAAAh" +
"AB6RGrfvAAAATgIAAAsAAAAAAAAAAAAAAAAAkwMAAF9yZWxzLy5yZWxzUEsBAi0AFAAGAAgAAAAh" +
"ANZks1H0AAAAMQMAABwAAAAAAAAAAAAAAAAAswYAAHdvcmQvX3JlbHMvZG9jdW1lbnQueG1sLnJl" +
"bHNQSwECLQAUAAYACAAAACEAKuHNzUwCAACRBgAAEQAAAAAAAAAAAAAAAADpCAAAd29yZC9kb2N1" +
"bWVudC54bWxQSwECLQAUAAYACAAAACEAqlIl3yMGAACLGgAAFQAAAAAAAAAAAAAAAABkCwAAd29y" +
"ZC90aGVtZS90aGVtZTEueG1sUEsBAi0AFAAGAAgAAAAhAJ9R6t6lAwAAsgkAABEAAAAAAAAAAAAA" +
"AAAAuhEAAHdvcmQvc2V0dGluZ3MueG1sUEsBAi0AFAAGAAgAAAAhAPameeXEAQAA7QQAABIAAAAA" +
"AAAAAAAAAAAAjhUAAHdvcmQvZm9udFRhYmxlLnhtbFBLAQItABQABgAIAAAAIQBbbf2TCQEAAPEB" +
"AAAUAAAAAAAAAAAAAAAAAIIXAAB3b3JkL3dlYlNldHRpbmdzLnhtbFBLAQItABQABgAIAAAAIQDr" +
"GxI5cgEAAMcCAAAQAAAAAAAAAAAAAAAAAL0YAABkb2NQcm9wcy9hcHAueG1sUEsBAi0AFAAGAAgA" +
"AAAhAKqkQahvAQAA6wIAABEAAAAAAAAAAAAAAAAAZRsAAGRvY1Byb3BzL2NvcmUueG1sUEsBAi0A" +
"FAAGAAgAAAAhAAtGahAbCwAABHAAAA8AAAAAAAAAAAAAAAAACx4AAHdvcmQvc3R5bGVzLnhtbFBL" +
"BQYAAAAACwALAMECAABTKQAAAAA=";

        var doc = new openXml.OpenXmlPackage();
        doc.openFromBase64Async(templateDocument, function (openedDocument) {
            var p = new XElement(W.p,
                new XElement(W.r,
                    new XElement(W.t, "Hello Open XML World.  Template document was initialized by a string literal.")));
            openedDocument.mainDocumentPart().getXDocument().descendants(W.p).firstOrDefault().addAfterSelf(p);

            openedDocument.saveToBase64Async(function (b64) {
                var s = "First 10 characters of the base64 string: " + b64.substring(0, 10);
                alert(s);
            });

            openedDocument.saveToBlobAsync(function (blob) {
                documentToSave = blob;
            });
        });

        root.saveDocument = function () {
            if (documentToSave === null) {
                alert("Document is not open");
            }
            else {
                saveAs(documentToSave, "SavedTemplateDocument.docx");
            }
        }

    }(this));

</script>
</pre>
    <div id="testContent"></div>
</body>
</html>
