/* Project specific JavaScript API library */

/*
	Copyright (c) Microsoft Corporation.  All rights reserved.
*/


/*
    Your use of this file is governed by the Microsoft Services Agreement http://go.microsoft.com/fwlink/?LinkId=266419.

    This file also contains the following Promise implementation (with a few small modifications):
        * @overview es6-promise - a tiny implementation of Promises/A+.
        * @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)
        * @license   Licensed under MIT license
        *            See https://raw.githubusercontent.com/jakearchibald/es6-promise/master/LICENSE
        * @version   2.3.0
*/
var __extends=this&&this.__extends||function(){var a=function(c,b){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(b,a){b.__proto__=a}||function(c,a){for(var b in a)if(a.hasOwnProperty(b))c[b]=a[b]};return a(c,b)};return function(c,b){a(c,b);function d(){this.constructor=c}c.prototype=b===null?Object.create(b):(d.prototype=b.prototype,new d)}}(),OfficeExt;(function(b){var a=function(){var a=true;function b(){}b.prototype.isMsAjaxLoaded=function(){var b="function",c="undefined";if(typeof Sys!==c&&typeof Type!==c&&Sys.StringBuilder&&typeof Sys.StringBuilder===b&&Type.registerNamespace&&typeof Type.registerNamespace===b&&Type.registerClass&&typeof Type.registerClass===b&&typeof Function._validateParams===b&&Sys.Serialization&&Sys.Serialization.JavaScriptSerializer&&typeof Sys.Serialization.JavaScriptSerializer.serialize===b)return a;else return false};b.prototype.loadMsAjaxFull=function(b){var a=(window.location.protocol.toLowerCase()==="https:"?"https:":"http:")+"//ajax.aspnetcdn.com/ajax/3.5/MicrosoftAjax.js";OSF.OUtil.loadScript(a,b)};Object.defineProperty(b.prototype,"msAjaxError",{"get":function(){var a=this;if(a._msAjaxError==null&&a.isMsAjaxLoaded())a._msAjaxError=Error;return a._msAjaxError},"set":function(a){this._msAjaxError=a},enumerable:a,configurable:a});Object.defineProperty(b.prototype,"msAjaxString",{"get":function(){var a=this;if(a._msAjaxString==null&&a.isMsAjaxLoaded())a._msAjaxString=String;return a._msAjaxString},"set":function(a){this._msAjaxString=a},enumerable:a,configurable:a});Object.defineProperty(b.prototype,"msAjaxDebug",{"get":function(){var a=this;if(a._msAjaxDebug==null&&a.isMsAjaxLoaded())a._msAjaxDebug=Sys.Debug;return a._msAjaxDebug},"set":function(a){this._msAjaxDebug=a},enumerable:a,configurable:a});return b}();b.MicrosoftAjaxFactory=a})(OfficeExt||(OfficeExt={}));var OsfMsAjaxFactory=new OfficeExt.MicrosoftAjaxFactory,OSF=OSF||{};(function(b){var a=function(){function a(a){this._internalStorage=a}a.prototype.getItem=function(a){try{return this._internalStorage&&this._internalStorage.getItem(a)}catch(b){return null}};a.prototype.setItem=function(b,a){try{this._internalStorage&&this._internalStorage.setItem(b,a)}catch(c){}};a.prototype.clear=function(){try{this._internalStorage&&this._internalStorage.clear()}catch(a){}};a.prototype.removeItem=function(a){try{this._internalStorage&&this._internalStorage.removeItem(a)}catch(b){}};a.prototype.getKeysWithPrefix=function(d){var b=[];try{for(var e=this._internalStorage&&this._internalStorage.length||0,a=0;a<e;a++){var c=this._internalStorage.key(a);c.indexOf(d)===0&&b.push(c)}}catch(f){}return b};a.prototype.isLocalStorageAvailable=function(){return this._internalStorage!=null};return a}();b.SafeStorage=a})(OfficeExt||(OfficeExt={}));OSF.XdmFieldName={ConversationUrl:"ConversationUrl",AppId:"AppId"};OSF.TestFlightStart=1e3;OSF.TestFlightEnd=1009;OSF.FlightNames={UseOriginNotUrl:0,AddinEnforceHttps:2,FirstPartyAnonymousProxyReadyCheckTimeout:6,AddinRibbonIdAllowUnknown:9,ManifestParserDevConsoleLog:15,AddinActionDefinitionHybridMode:18,UseActionIdForUILessCommand:20,RequirementSetRibbonApiOnePointTwo:21,SetFocusToTaskpaneIsEnabled:22,ShortcutInfoArrayInUserPreferenceData:23,OSFTestFlight1000:OSF.TestFlightStart,OSFTestFlight1001:OSF.TestFlightStart+1,OSFTestFlight1002:OSF.TestFlightStart+2,OSFTestFlight1003:OSF.TestFlightStart+3,OSFTestFlight1004:OSF.TestFlightStart+4,OSFTestFlight1005:OSF.TestFlightStart+5,OSFTestFlight1006:OSF.TestFlightStart+6,OSFTestFlight1007:OSF.TestFlightStart+7,OSFTestFlight1008:OSF.TestFlightStart+8,OSFTestFlight1009:OSF.TestFlightEnd};OSF.TrustUXFlightValues={TrustUXControlA:0,TrustUXExperimentB:1,TrustUXExperimentC:2};OSF.FlightTreatmentNames={AllowStorageAccessByUserActivationOnIFrameCheck:"Microsoft.Office.SharedOnline.AllowStorageAccessByUserActivationOnIFrameCheck",WopiPreinstalledAddInsEnabled:"Microsoft.Office.SharedOnline.WopiPreinstalledAddInsEnabled",WopiUseNewActivate:"Microsoft.Office.SharedOnline.WopiUseNewActivate",CheckProxyIsReadyRetry:"Microsoft.Office.SharedOnline.OEP.CheckProxyIsReadyRetry",InsertionDialogFixesEnabled:"Microsoft.Office.SharedOnline.InsertionDialogFixesEnabled",BlockAutoOpenAddInIfStoreDisabled:"Microsoft.Office.SharedOnline.BlockAutoOpenAddInIfStoreDisabled",AddinTrustUXImprovement:"Microsoft.Office.SharedOnline.AddinTrustUXImprovement",TeachingUIForPrivateCatelogEnabled:"Microsoft.Office.SharedOnline.TeachingUIForPrivateCatelogEnabled"};OSF.Flights=[];OSF.IntFlights={};OSF.Settings={};OSF.WindowNameItemKeys={BaseFrameName:"baseFrameName",HostInfo:"hostInfo",XdmInfo:"xdmInfo",SerializerVersion:"serializerVersion",AppContext:"appContext",Flights:"flights"};OSF.OUtil=function(){var l="focus",k="https:",j="on",q="configurable",p="writable",i="enumerable",e="",f="undefined",c=false,b=true,h="string",m=2147483647,a=null,g="#",d=-1,w=d,C="&_xdm_Info=",z="&_serializer_version=",B="&_flights=",A="_xdm_",F="_serializer_version=",G="_flights=",s=g,y="&",n="class",v={},E=3e4,r=a,u=a,o=(new Date).getTime();function D(){var a=m*Math.random();a^=o^(new Date).getMilliseconds()<<Math.floor(Math.random()*(31-10));return a.toString(16)}function t(){if(!r){try{var b=window.sessionStorage}catch(c){b=a}r=new OfficeExt.SafeStorage(b)}return r}function x(e){for(var c=[],b=[],f=e.length,a,d=0;d<f;d++){a=e[d];if(a.tabIndex)if(a.tabIndex>0)b.push(a);else a.tabIndex===0&&c.push(a);else c.push(a)}b=b.sort(function(d,c){var a=d.tabIndex-c.tabIndex;if(a===0)a=b.indexOf(d)-b.indexOf(c);return a});return [].concat(b,c)}return {set_entropy:function(a){if(typeof a==h)for(var b=0;b<a.length;b+=4){for(var d=0,c=0;c<4&&b+c<a.length;c++)d=(d<<8)+a.charCodeAt(b+c);o^=d}else if(typeof a=="number")o^=a;else o^=m*Math.random();o&=m},extend:function(b,a){var c=function(){};c.prototype=a.prototype;b.prototype=new c;b.prototype.constructor=b;b.uber=a.prototype;if(a.prototype.constructor===Object.prototype.constructor)a.prototype.constructor=a},setNamespace:function(b,a){if(a&&b&&!a[b])a[b]={}},unsetNamespace:function(b,a){if(a&&b&&a[b])delete a[b]},serializeSettings:function(b){var d={};for(var c in b){var a=b[c];try{if(JSON)a=JSON.stringify(a,function(a,b){return OSF.OUtil.isDate(this[a])?OSF.DDA.SettingsManager.DateJSONPrefix+this[a].getTime()+OSF.DDA.SettingsManager.DataJSONSuffix:b});else a=Sys.Serialization.JavaScriptSerializer.serialize(a);d[c]=a}catch(e){}}return d},deserializeSettings:function(c){var f={};c=c||{};for(var e in c){var a=c[e];try{if(JSON)a=JSON.parse(a,function(c,a){var b;if(typeof a===h&&a&&a.length>6&&a.slice(0,5)===OSF.DDA.SettingsManager.DateJSONPrefix&&a.slice(d)===OSF.DDA.SettingsManager.DataJSONSuffix){b=new Date(parseInt(a.slice(5,d)));if(b)return b}return a});else a=Sys.Serialization.JavaScriptSerializer.deserialize(a,b);f[e]=a}catch(g){}}return f},loadScript:function(f,g,i){if(f&&g){var k=window.document,d=v[f];if(!d){var e=k.createElement("script");e.type="text/javascript";d={loaded:c,pendingCallbacks:[g],timer:a};v[f]=d;var j=function(){if(d.timer!=a){clearTimeout(d.timer);delete d.timer}d.loaded=b;for(var e=d.pendingCallbacks.length,c=0;c<e;c++){var f=d.pendingCallbacks.shift();f()}},l=function(){if(window.navigator.userAgent.indexOf("Trident")>0)h(a);else h(new Event("Script load timed out"))},h=function(){delete v[f];if(d.timer!=a){clearTimeout(d.timer);delete d.timer}for(var c=d.pendingCallbacks.length,b=0;b<c;b++){var e=d.pendingCallbacks.shift();e()}};if(e.readyState)e.onreadystatechange=function(){if(e.readyState=="loaded"||e.readyState=="complete"){e.onreadystatechange=a;j()}};else e.onload=j;e.onerror=h;i=i||E;d.timer=setTimeout(l,i);e.setAttribute("crossOrigin","anonymous");e.src=f;k.getElementsByTagName("head")[0].appendChild(e)}else if(d.loaded)g();else d.pendingCallbacks.push(g)}},loadCSS:function(c){if(c){var b=window.document,a=b.createElement("link");a.type="text/css";a.rel="stylesheet";a.href=c;b.getElementsByTagName("head")[0].appendChild(a)}},parseEnum:function(b,c){var a=c[b.trim()];if(typeof a==f){OsfMsAjaxFactory.msAjaxDebug.trace("invalid enumeration string:"+b);throw OsfMsAjaxFactory.msAjaxError.argument("str")}return a},delayExecutionAndCache:function(){var a={calc:arguments[0]};return function(){if(a.calc){a.val=a.calc.apply(this,arguments);delete a.calc}return a.val}},getUniqueId:function(){w=w+1;return w.toString()},formatString:function(){var a=arguments,b=a[0];return b.replace(/{(\d+)}/gm,function(d,b){var c=parseInt(b,10)+1;return a[c]===undefined?"{"+b+"}":a[c]})},generateConversationId:function(){return [D(),D(),(new Date).getTime().toString()].join("_")},getFrameName:function(a){return A+a+this.generateConversationId()},addXdmInfoAsHash:function(b,a){return OSF.OUtil.addInfoAsHash(b,C,a,c)},addSerializerVersionAsHash:function(c,a){return OSF.OUtil.addInfoAsHash(c,z,a,b)},addFlightsAsHash:function(c,a){return OSF.OUtil.addInfoAsHash(c,B,a,b)},addInfoAsHash:function(b,g,c,i){b=b.trim()||e;var f=b.split(s),h=f.shift(),d=f.join(s),a;if(i)a=[g,encodeURIComponent(c),d].join(e);else a=[d,g,c].join(e);return [h,s,a].join(e)},parseHostInfoFromWindowName:function(a,b){return OSF.OUtil.parseInfoFromWindowName(a,b,OSF.WindowNameItemKeys.HostInfo)},parseXdmInfo:function(b){var a=OSF.OUtil.parseXdmInfoWithGivenFragment(b,window.location.hash);if(!a)a=OSF.OUtil.parseXdmInfoFromWindowName(b,window.name);return a},parseXdmInfoFromWindowName:function(a,b){return OSF.OUtil.parseInfoFromWindowName(a,b,OSF.WindowNameItemKeys.XdmInfo)},parseXdmInfoWithGivenFragment:function(a,b){return OSF.OUtil.parseInfoWithGivenFragment(C,A,c,a,b)},parseSerializerVersion:function(b){var a=OSF.OUtil.parseSerializerVersionWithGivenFragment(b,window.location.hash);if(isNaN(a))a=OSF.OUtil.parseSerializerVersionFromWindowName(b,window.name);return a},parseSerializerVersionFromWindowName:function(a,b){return parseInt(OSF.OUtil.parseInfoFromWindowName(a,b,OSF.WindowNameItemKeys.SerializerVersion))},parseSerializerVersionWithGivenFragment:function(a,c){return parseInt(OSF.OUtil.parseInfoWithGivenFragment(z,F,b,a,c))},parseFlights:function(b){var a=OSF.OUtil.parseFlightsWithGivenFragment(b,window.location.hash);if(a.length==0)a=OSF.OUtil.parseFlightsFromWindowName(b,window.name);return a},checkFlight:function(a){return OSF.Flights&&OSF.Flights.indexOf(a)>=0},pushFlight:function(a){if(OSF.Flights.indexOf(a)<0){OSF.Flights.push(a);return b}return c},getBooleanSetting:function(a){return OSF.OUtil.getBooleanFromDictionary(OSF.Settings,a)},getBooleanFromDictionary:function(b,a){var d=b&&a&&b[a]!==undefined&&b[a]&&(typeof b[a]===h&&b[a].toUpperCase()==="TRUE"||typeof b[a]==="boolean"&&b[a]);return d!==undefined?d:c},getIntFromDictionary:function(b,a){if(b&&a&&b[a]!==undefined&&typeof b[a]===h)return parseInt(b[a]);else return NaN},pushIntFlight:function(a,d){if(!(a in OSF.IntFlights)){OSF.IntFlights[a]=d;return b}return c},getIntFlight:function(a){if(OSF.IntFlights&&a in OSF.IntFlights)return OSF.IntFlights[a];else return NaN},parseFlightsFromWindowName:function(a,b){return OSF.OUtil.parseArrayWithDefault(OSF.OUtil.parseInfoFromWindowName(a,b,OSF.WindowNameItemKeys.Flights))},parseFlightsWithGivenFragment:function(a,c){return OSF.OUtil.parseArrayWithDefault(OSF.OUtil.parseInfoWithGivenFragment(B,G,b,a,c))},parseArrayWithDefault:function(b){var a=[];try{a=JSON.parse(b)}catch(c){}if(!Array.isArray(a))a=[];return a},parseInfoFromWindowName:function(g,h,f){try{var b=JSON.parse(h),c=b!=a?b[f]:a,d=t();if(!g&&d&&b!=a){var e=b[OSF.WindowNameItemKeys.BaseFrameName]+f;if(c)d.setItem(e,c);else c=d.getItem(e)}return c}catch(i){return a}},parseInfoWithGivenFragment:function(m,j,k,i,l){var f=l.split(m),b=f.length>1?f[f.length-1]:a;if(k&&b!=a){if(b.indexOf(y)>=0)b=b.split(y)[0];b=decodeURIComponent(b)}var c=t();if(!i&&c){var e=window.name.indexOf(j);if(e>d){var g=window.name.indexOf(";",e);if(g==d)g=window.name.length;var h=window.name.substring(e,g);if(b)c.setItem(h,b);else b=c.getItem(h)}}return b},getConversationId:function(){var c=window.location.search,b=a;if(c){var d=c.indexOf("&");b=d>0?c.substring(1,d):c.substr(1);if(b&&b.charAt(b.length-1)==="="){b=b.substring(0,b.length-1);if(b)b=decodeURIComponent(b)}}return b},getInfoItems:function(b){var a=b.split("$");if(typeof a[1]==f)a=b.split("|");if(typeof a[1]==f)a=b.split("%7C");return a},getXdmFieldValue:function(f,d){var b=e,c=OSF.OUtil.parseXdmInfo(d);if(c){var a=OSF.OUtil.getInfoItems(c);if(a!=undefined&&a.length>=3)switch(f){case OSF.XdmFieldName.ConversationUrl:b=a[2];break;case OSF.XdmFieldName.AppId:b=a[1]}}return b},validateParamObject:function(f,e){var a=Function._validateParams(arguments,[{name:"params",type:Object,mayBeNull:c},{name:"expectedProperties",type:Object,mayBeNull:c},{name:"callback",type:Function,mayBeNull:b}]);if(a)throw a;for(var d in e){a=Function._validateParameter(f[d],e[d],d);if(a)throw a}},writeProfilerMark:function(a){if(window.msWriteProfilerMark){window.msWriteProfilerMark(a);OsfMsAjaxFactory.msAjaxDebug.trace(a)}},outputDebug:function(a){typeof OsfMsAjaxFactory!==f&&OsfMsAjaxFactory.msAjaxDebug&&OsfMsAjaxFactory.msAjaxDebug.trace&&OsfMsAjaxFactory.msAjaxDebug.trace(a)},defineNondefaultProperty:function(e,f,a,c){a=a||{};for(var g in c){var d=c[g];if(a[d]==undefined)a[d]=b}Object.defineProperty(e,f,a);return e},defineNondefaultProperties:function(c,a,d){a=a||{};for(var b in a)OSF.OUtil.defineNondefaultProperty(c,b,a[b],d);return c},defineEnumerableProperty:function(c,b,a){return OSF.OUtil.defineNondefaultProperty(c,b,a,[i])},defineEnumerableProperties:function(b,a){return OSF.OUtil.defineNondefaultProperties(b,a,[i])},defineMutableProperty:function(c,b,a){return OSF.OUtil.defineNondefaultProperty(c,b,a,[p,i,q])},defineMutableProperties:function(b,a){return OSF.OUtil.defineNondefaultProperties(b,a,[p,i,q])},finalizeProperties:function(e,d){d=d||{};for(var g=Object.getOwnPropertyNames(e),i=g.length,f=0;f<i;f++){var h=g[f],a=Object.getOwnPropertyDescriptor(e,h);if(!a.get&&!a.set)a.writable=d.writable||c;a.configurable=d.configurable||c;a.enumerable=d.enumerable||b;Object.defineProperty(e,h,a)}return e},mapList:function(a,c){var b=[];if(a)for(var d in a)b.push(c(a[d]));return b},listContainsKey:function(d,e){for(var a in d)if(e==a)return b;return c},listContainsValue:function(a,d){for(var e in a)if(d==a[e])return b;return c},augmentList:function(a,b){var d=a.push?function(c,b){a.push(b)}:function(c,b){a[c]=b};for(var c in b)d(c,b[c])},redefineList:function(a,b){for(var d in a)delete a[d];for(var c in b)a[c]=b[c]},isArray:function(a){return Object.prototype.toString.apply(a)==="[object Array]"},isFunction:function(a){return Object.prototype.toString.apply(a)==="[object Function]"},isDate:function(a){return Object.prototype.toString.apply(a)==="[object Date]"},addEventListener:function(a,b,d){if(a.addEventListener)a.addEventListener(b,d,c);else if(Sys.Browser.agent===Sys.Browser.InternetExplorer&&a.attachEvent)a.attachEvent(j+b,d);else a[j+b]=d},removeEventListener:function(b,d,e){if(b.removeEventListener)b.removeEventListener(d,e,c);else if(Sys.Browser.agent===Sys.Browser.InternetExplorer&&b.detachEvent)b.detachEvent(j+d,e);else b[j+d]=a},xhrGet:function(f,e,c){var a;try{a=new XMLHttpRequest;a.onreadystatechange=function(){if(a.readyState==4)if(a.status==200)e(a.responseText);else c(a.status)};a.open("GET",f,b);a.send()}catch(d){c(d)}},encodeBase64:function(c){if(!c)return c;var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",m=[],b=[],i=0,k,h,j,d,f,g,a,n=c.length;do{k=c.charCodeAt(i++);h=c.charCodeAt(i++);j=c.charCodeAt(i++);a=0;d=k&255;f=k>>8;g=h&255;b[a++]=d>>2;b[a++]=(d&3)<<4|f>>4;b[a++]=(f&15)<<2|g>>6;b[a++]=g&63;if(!isNaN(h)){d=h>>8;f=j&255;g=j>>8;b[a++]=d>>2;b[a++]=(d&3)<<4|f>>4;b[a++]=(f&15)<<2|g>>6;b[a++]=g&63}if(isNaN(h))b[a-1]=64;else if(isNaN(j)){b[a-2]=64;b[a-1]=64}for(var l=0;l<a;l++)m.push(o.charAt(b[l]))}while(i<n);return m.join(e)},getSessionStorage:function(){return t()},getLocalStorage:function(){if(!u){try{var b=window.localStorage}catch(c){b=a}u=new OfficeExt.SafeStorage(b)}return u},convertIntToCssHexColor:function(b){var a=g+(Number(b)+16777216).toString(16).slice(-6);return a},attachClickHandler:function(a,b){a.onclick=function(){b()};a.ontouchend=function(a){b();a.preventDefault()}},getQueryStringParamValue:function(a,d){var f=Function._validateParams(arguments,[{name:"queryString",type:String,mayBeNull:c},{name:"paramName",type:String,mayBeNull:c}]);if(f){OsfMsAjaxFactory.msAjaxDebug.trace("OSF_Outil_getQueryStringParamValue: Parameters cannot be null.");return e}var b=new RegExp("[\\?&]"+d+"=([^&#]*)","i");if(!b.test(a)){OsfMsAjaxFactory.msAjaxDebug.trace("OSF_Outil_getQueryStringParamValue: The parameter is not found.");return e}return b.exec(a)[1]},getHostnamePortionForLogging:function(d){var f=Function._validateParams(arguments,[{name:"hostname",type:String,mayBeNull:c}]);if(f)return e;var a=d.split("."),b=a.length;if(b>=2)return a[b-2]+"."+a[b-1];else if(b==1)return a[0]},isiOS:function(){return window.navigator.userAgent.match(/(iPad|iPhone|iPod)/g)?b:c},isChrome:function(){return window.navigator.userAgent.indexOf("Chrome")>0&&!OSF.OUtil.isEdge()},isEdge:function(){return window.navigator.userAgent.indexOf("Edge")>0},isIE:function(){return window.navigator.userAgent.indexOf("Trident")>0},isFirefox:function(){return window.navigator.userAgent.indexOf("Firefox")>0},startsWith:function(b,a,c){if(c)return b.substr(0,a.length)===a;else return b.startsWith(a)},containsPort:function(d,e,c,a){return this.startsWith(d,e+"//"+c+":"+a,b)||this.startsWith(d,c+":"+a,b)},getRedundandPortString:function(b,a){if(!b||!a)return e;if(a.protocol==k&&this.containsPort(b,k,a.hostname,"443"))return ":443";else if(a.protocol=="http:"&&this.containsPort(b,"http:",a.hostname,"80"))return ":80";return e},removeChar:function(a,b){if(b<a.length-1)return a.substring(0,b)+a.substring(b+1);else if(b==a.length-1)return a.substring(0,a.length-1);else return a},cleanUrlOfChar:function(a,c){for(var b=0;b<a.length;b++)if(a.charAt(b)===c)if(b+1>=a.length)return this.removeChar(a,b);else if(c==="/"){if(a.charAt(b+1)==="?"||a.charAt(b+1)===g)return this.removeChar(a,b)}else if(c==="?")if(a.charAt(b+1)===g)return this.removeChar(a,b);return a},cleanUrl:function(a){a=this.cleanUrlOfChar(a,"/");a=this.cleanUrlOfChar(a,"?");a=this.cleanUrlOfChar(a,g);if(a.substr(0,8)=="https://"){var b=a.indexOf(":443");if(b!=d)if(b==a.length-4||a.charAt(b+4)=="/"||a.charAt(b+4)=="?"||a.charAt(b+4)==g)a=a.substring(0,b)+a.substring(b+4)}else if(a.substr(0,7)=="http://"){var b=a.indexOf(":80");if(b!=d)if(b==a.length-3||a.charAt(b+3)=="/"||a.charAt(b+3)=="?"||a.charAt(b+3)==g)a=a.substring(0,b)+a.substring(b+3)}return a},parseUrl:function(g,i){var h=this;if(i===void 0)i=c;if(typeof g===f||!g)return undefined;var j="NotHttps",o="InvalidUrl",n=h.isIE(),b={protocol:undefined,hostname:undefined,host:undefined,port:undefined,pathname:undefined,search:undefined,hash:undefined,isPortPartOfUrl:undefined};try{if(n){var a=document.createElement("a");a.href=g;if(!a||!a.protocol||!a.host||!a.hostname||!a.href||h.cleanUrl(a.href).toLowerCase()!==h.cleanUrl(g).toLowerCase())throw o;if(OSF.OUtil.checkFlight(OSF.FlightNames.AddinEnforceHttps))if(i&&a.protocol!=k)throw new Error(j);var m=h.getRedundandPortString(g,a);b.protocol=a.protocol;b.hostname=a.hostname;b.port=m==e?a.port:e;b.host=m!=e?a.hostname:a.host;b.pathname=(n?"/":e)+a.pathname;b.search=a.search;b.hash=a.hash;b.isPortPartOfUrl=h.containsPort(g,a.protocol,a.hostname,a.port)}else{var d=new URL(g);if(d&&d.protocol&&d.host&&d.hostname){if(OSF.OUtil.checkFlight(OSF.FlightNames.AddinEnforceHttps))if(i&&d.protocol!=k)throw new Error(j);b.protocol=d.protocol;b.hostname=d.hostname;b.port=d.port;b.host=d.host;b.pathname=d.pathname;b.search=d.search;b.hash=d.hash;b.isPortPartOfUrl=d.host.lastIndexOf(":"+d.port)==d.host.length-d.port.length-1}}}catch(l){if(l.message===j)throw l}return b},shallowCopy:function(b){if(b==a)return a;else if(!(b instanceof Object))return b;else if(Array.isArray(b)){for(var e=[],d=0;d<b.length;d++)e.push(b[d]);return e}else{var f=b.constructor();for(var c in b)if(b.hasOwnProperty(c))f[c]=b[c];return f}},createObject:function(b){var d=a;if(b){d={};for(var e=b.length,c=0;c<e;c++)d[b[c].name]=b[c].value}return d},addClass:function(a,b){if(!OSF.OUtil.hasClass(a,b)){var c=a.getAttribute(n);if(c)a.setAttribute(n,c+" "+b);else a.setAttribute(n,b)}},removeClass:function(b,c){if(OSF.OUtil.hasClass(b,c)){var a=b.getAttribute(n),d=new RegExp("(\\s|^)"+c+"(\\s|$)");a=a.replace(d,e);b.setAttribute(n,a)}},hasClass:function(c,b){var a=c.getAttribute(n);return a&&a.match(new RegExp("(\\s|^)"+b+"(\\s|$)"))},focusToFirstTabbable:function(e,i){var g,h=c,f,j=function(){h=b},k=function(c,a,b){if(a<0||a>c)return d;else if(a===0&&b)return d;else if(a===c-1&&!b)return d;if(b)return a-1;else return a+1};e=x(e);g=i?e.length-1:0;if(e.length===0)return a;while(!h&&g>=0&&g<e.length){f=e[g];window.focus();f.addEventListener(l,j);f.focus();f.removeEventListener(l,j);g=k(e.length,g,i);if(!h&&f===document.activeElement)h=b}if(h)return f;else return a},focusToNextTabbable:function(f,o,m){var j,e,h=c,g,k=function(){h=b},n=function(b,c){for(var a=0;a<b.length;a++)if(b[a]===c)return a;return d},i=function(c,a,b){if(a<0||a>c)return d;else if(a===0&&b)return d;else if(a===c-1&&!b)return d;if(b)return a-1;else return a+1};f=x(f);j=n(f,o);e=i(f.length,j,m);if(e<0)return a;while(!h&&e>=0&&e<f.length){g=f[e];g.addEventListener(l,k);g.focus();g.removeEventListener(l,k);e=i(f.length,e,m);if(!h&&g===document.activeElement)h=b}if(h)return g;else return a},isNullOrUndefined:function(d){if(typeof d===f)return b;if(d===a)return b;return c},stringEndsWith:function(d,a){if(!OSF.OUtil.isNullOrUndefined(d)&&!OSF.OUtil.isNullOrUndefined(a)){if(a.length>d.length)return c;if(d.substr(d.length-a.length)===a)return b}return c},hashCode:function(b){var a=0;if(!OSF.OUtil.isNullOrUndefined(b)){var c=0,d=b.length;while(c<d)a=(a<<5)-a+b.charCodeAt(c++)|0}return a},getValue:function(a,b){if(OSF.OUtil.isNullOrUndefined(a))return b;return a},externalNativeFunctionExists:function(a){return a==="unknown"||a!==f}}}();OSF.OUtil.Guid=function(){var a=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];return {generateNewGuid:function(){for(var c="",d=(new Date).getTime(),b=0;b<32&&d>0;b++){if(b==8||b==12||b==16||b==20)c+="-";c+=a[d%16];d=Math.floor(d/16)}for(;b<32;b++){if(b==8||b==12||b==16||b==20)c+="-";c+=a[Math.floor(Math.random()*16)]}return c}}}();try{(function(){OSF.Flights=OSF.OUtil.parseFlights(true)})()}catch(ex){}window.OSF=OSF;OSF.OUtil.setNamespace("OSF",window);OSF.MessageIDs={FetchBundleUrl:0,LoadReactBundle:1,LoadBundleSuccess:2,LoadBundleError:3};OSF.AppName={Unsupported:0,Excel:1,Word:2,PowerPoint:4,Outlook:8,ExcelWebApp:16,WordWebApp:32,OutlookWebApp:64,Project:128,AccessWebApp:256,PowerpointWebApp:512,ExcelIOS:1024,Sway:2048,WordIOS:4096,PowerPointIOS:8192,Access:16384,Lync:32768,OutlookIOS:65536,OneNoteWebApp:131072,OneNote:262144,ExcelWinRT:524288,WordWinRT:1048576,PowerpointWinRT:2097152,OutlookAndroid:4194304,OneNoteWinRT:8388608,ExcelAndroid:8388609,VisioWebApp:8388610,OneNoteIOS:8388611,WordAndroid:8388613,PowerpointAndroid:8388614,Visio:8388615,OneNoteAndroid:4194305};OSF.InternalPerfMarker={DataCoercionBegin:"Agave.HostCall.CoerceDataStart",DataCoercionEnd:"Agave.HostCall.CoerceDataEnd"};OSF.HostCallPerfMarker={IssueCall:"Agave.HostCall.IssueCall",ReceiveResponse:"Agave.HostCall.ReceiveResponse",RuntimeExceptionRaised:"Agave.HostCall.RuntimeExecptionRaised"};OSF.AgaveHostAction={Select:0,UnSelect:1,CancelDialog:2,InsertAgave:3,CtrlF6In:4,CtrlF6Exit:5,CtrlF6ExitShift:6,SelectWithError:7,NotifyHostError:8,RefreshAddinCommands:9,PageIsReady:10,TabIn:11,TabInShift:12,TabExit:13,TabExitShift:14,EscExit:15,F2Exit:16,ExitNoFocusable:17,ExitNoFocusableShift:18,MouseEnter:19,MouseLeave:20,UpdateTargetUrl:21,InstallCustomFunctions:22,SendTelemetryEvent:23,UninstallCustomFunctions:24,SendMessage:25,LaunchExtensionComponent:26,StopExtensionComponent:27,RestartExtensionComponent:28,EnableTaskPaneHeaderButton:29,DisableTaskPaneHeaderButton:30,TaskPaneHeaderButtonClicked:31,RemoveAppCommandsAddin:32,RefreshRibbonGallery:33,GetOriginalControlId:34,OfficeJsReady:35,InsertDevManifest:36,InsertDevManifestError:37,SendCustomerContent:38,KeyboardShortcuts:39};OSF.SharedConstants={NotificationConversationIdSuffix:"_ntf"};OSF.DialogMessageType={DialogMessageReceived:0,DialogParentMessageReceived:1,DialogClosed:12006};OSF.OfficeAppContext=function(B,x,s,p,u,y,t,w,A,k,z,m,l,o,i,h,g,f,j,c,e,v,r,b,n,q,d){var a=this;a._id=B;a._appName=x;a._appVersion=s;a._appUILocale=p;a._dataLocale=u;a._docUrl=y;a._clientMode=t;a._settings=w;a._reason=A;a._osfControlType=k;a._eToken=z;a._correlationId=m;a._appInstanceId=l;a._touchEnabled=o;a._commerceAllowed=i;a._appMinorVersion=h;a._requirementMatrix=g;a._hostCustomMessage=f;a._hostFullVersion=j;a._isDialog=false;a._clientWindowHeight=c;a._clientWindowWidth=e;a._addinName=v;a._appDomains=r;a._dialogRequirementMatrix=b;a._featureGates=n;a._officeTheme=q;a._initialDisplayMode=d;a.get_id=function(){return this._id};a.get_appName=function(){return this._appName};a.get_appVersion=function(){return this._appVersion};a.get_appUILocale=function(){return this._appUILocale};a.get_dataLocale=function(){return this._dataLocale};a.get_docUrl=function(){return this._docUrl};a.get_clientMode=function(){return this._clientMode};a.get_bindings=function(){return this._bindings};a.get_settings=function(){return this._settings};a.get_reason=function(){return this._reason};a.get_osfControlType=function(){return this._osfControlType};a.get_eToken=function(){return this._eToken};a.get_correlationId=function(){return this._correlationId};a.get_appInstanceId=function(){return this._appInstanceId};a.get_touchEnabled=function(){return this._touchEnabled};a.get_commerceAllowed=function(){return this._commerceAllowed};a.get_appMinorVersion=function(){return this._appMinorVersion};a.get_requirementMatrix=function(){return this._requirementMatrix};a.get_dialogRequirementMatrix=function(){return this._dialogRequirementMatrix};a.get_hostCustomMessage=function(){return this._hostCustomMessage};a.get_hostFullVersion=function(){return this._hostFullVersion};a.get_isDialog=function(){return this._isDialog};a.get_clientWindowHeight=function(){return this._clientWindowHeight};a.get_clientWindowWidth=function(){return this._clientWindowWidth};a.get_addinName=function(){return this._addinName};a.get_appDomains=function(){return this._appDomains};a.get_featureGates=function(){return this._featureGates};a.get_officeTheme=function(){return this._officeTheme};a.get_initialDisplayMode=function(){return this._initialDisplayMode?this._initialDisplayMode:0}};OSF.OsfControlType={DocumentLevel:0,ContainerLevel:1};OSF.ClientMode={ReadOnly:0,ReadWrite:1};OSF.OUtil.setNamespace("Microsoft",window);OSF.OUtil.setNamespace("Office",Microsoft);OSF.OUtil.setNamespace("Client",Microsoft.Office);OSF.OUtil.setNamespace("WebExtension",Microsoft.Office);Microsoft.Office.WebExtension.InitializationReason={Inserted:"inserted",DocumentOpened:"documentOpened",ControlActivation:"controlActivation"};Microsoft.Office.WebExtension.ValueFormat={Unformatted:"unformatted",Formatted:"formatted"};Microsoft.Office.WebExtension.FilterType={All:"all"};Microsoft.Office.WebExtension.Parameters={BindingType:"bindingType",CoercionType:"coercionType",ValueFormat:"valueFormat",FilterType:"filterType",Columns:"columns",SampleData:"sampleData",GoToType:"goToType",SelectionMode:"selectionMode",Id:"id",PromptText:"promptText",ItemName:"itemName",FailOnCollision:"failOnCollision",StartRow:"startRow",StartColumn:"startColumn",RowCount:"rowCount",ColumnCount:"columnCount",Callback:"callback",AsyncContext:"asyncContext",Data:"data",Rows:"rows",OverwriteIfStale:"overwriteIfStale",FileType:"fileType",EventType:"eventType",Handler:"handler",SliceSize:"sliceSize",SliceIndex:"sliceIndex",ActiveView:"activeView",Status:"status",PlatformType:"platformType",HostType:"hostType",ForceConsent:"forceConsent",ForceAddAccount:"forceAddAccount",AuthChallenge:"authChallenge",AllowConsentPrompt:"allowConsentPrompt",ForMSGraphAccess:"forMSGraphAccess",AllowSignInPrompt:"allowSignInPrompt",JsonPayload:"jsonPayload",EnableNewHosts:"enableNewHosts",AccountTypeFilter:"accountTypeFilter",AddinTrustId:"addinTrustId",Reserved:"reserved",Tcid:"tcid",Xml:"xml",Namespace:"namespace",Prefix:"prefix",XPath:"xPath",Text:"text",ImageLeft:"imageLeft",ImageTop:"imageTop",ImageWidth:"imageWidth",ImageHeight:"imageHeight",TaskId:"taskId",FieldId:"fieldId",FieldValue:"fieldValue",ServerUrl:"serverUrl",ListName:"listName",ResourceId:"resourceId",ViewType:"viewType",ViewName:"viewName",GetRawValue:"getRawValue",CellFormat:"cellFormat",TableOptions:"tableOptions",TaskIndex:"taskIndex",ResourceIndex:"resourceIndex",CustomFieldId:"customFieldId",Url:"url",MessageHandler:"messageHandler",Width:"width",Height:"height",RequireHTTPs:"requireHTTPS",MessageToParent:"messageToParent",DisplayInIframe:"displayInIframe",MessageContent:"messageContent",HideTitle:"hideTitle",UseDeviceIndependentPixels:"useDeviceIndependentPixels",PromptBeforeOpen:"promptBeforeOpen",EnforceAppDomain:"enforceAppDomain",UrlNoHostInfo:"urlNoHostInfo",TargetOrigin:"targetOrigin",AppCommandInvocationCompletedData:"appCommandInvocationCompletedData",Base64:"base64",FormId:"formId"};OSF.OUtil.setNamespace("DDA",OSF);OSF.DDA.DocumentMode={ReadOnly:1,ReadWrite:0};OSF.DDA.PropertyDescriptors={AsyncResultStatus:"AsyncResultStatus"};OSF.DDA.EventDescriptors={};OSF.DDA.ListDescriptors={};OSF.DDA.UI={};OSF.DDA.getXdmEventName=function(b,a){if(a==Microsoft.Office.WebExtension.EventType.BindingSelectionChanged||a==Microsoft.Office.WebExtension.EventType.BindingDataChanged||a==Microsoft.Office.WebExtension.EventType.DataNodeDeleted||a==Microsoft.Office.WebExtension.EventType.DataNodeInserted||a==Microsoft.Office.WebExtension.EventType.DataNodeReplaced)return b+"_"+a;else return a};OSF.DDA.MethodDispId={dispidMethodMin:64,dispidGetSelectedDataMethod:64,dispidSetSelectedDataMethod:65,dispidAddBindingFromSelectionMethod:66,dispidAddBindingFromPromptMethod:67,dispidGetBindingMethod:68,dispidReleaseBindingMethod:69,dispidGetBindingDataMethod:70,dispidSetBindingDataMethod:71,dispidAddRowsMethod:72,dispidClearAllRowsMethod:73,dispidGetAllBindingsMethod:74,dispidLoadSettingsMethod:75,dispidSaveSettingsMethod:76,dispidGetDocumentCopyMethod:77,dispidAddBindingFromNamedItemMethod:78,dispidAddColumnsMethod:79,dispidGetDocumentCopyChunkMethod:80,dispidReleaseDocumentCopyMethod:81,dispidNavigateToMethod:82,dispidGetActiveViewMethod:83,dispidGetDocumentThemeMethod:84,dispidGetOfficeThemeMethod:85,dispidGetFilePropertiesMethod:86,dispidClearFormatsMethod:87,dispidSetTableOptionsMethod:88,dispidSetFormatsMethod:89,dispidExecuteRichApiRequestMethod:93,dispidAppCommandInvocationCompletedMethod:94,dispidCloseContainerMethod:97,dispidGetAccessTokenMethod:98,dispidGetAuthContextMethod:99,dispidOpenBrowserWindow:102,dispidCreateDocumentMethod:105,dispidInsertFormMethod:106,dispidDisplayRibbonCalloutAsyncMethod:109,dispidGetSelectedTaskMethod:110,dispidGetSelectedResourceMethod:111,dispidGetTaskMethod:112,dispidGetResourceFieldMethod:113,dispidGetWSSUrlMethod:114,dispidGetTaskFieldMethod:115,dispidGetProjectFieldMethod:116,dispidGetSelectedViewMethod:117,dispidGetTaskByIndexMethod:118,dispidGetResourceByIndexMethod:119,dispidSetTaskFieldMethod:120,dispidSetResourceFieldMethod:121,dispidGetMaxTaskIndexMethod:122,dispidGetMaxResourceIndexMethod:123,dispidCreateTaskMethod:124,dispidAddDataPartMethod:128,dispidGetDataPartByIdMethod:129,dispidGetDataPartsByNamespaceMethod:130,dispidGetDataPartXmlMethod:131,dispidGetDataPartNodesMethod:132,dispidDeleteDataPartMethod:133,dispidGetDataNodeValueMethod:134,dispidGetDataNodeXmlMethod:135,dispidGetDataNodesMethod:136,dispidSetDataNodeValueMethod:137,dispidSetDataNodeXmlMethod:138,dispidAddDataNamespaceMethod:139,dispidGetDataUriByPrefixMethod:140,dispidGetDataPrefixByUriMethod:141,dispidGetDataNodeTextMethod:142,dispidSetDataNodeTextMethod:143,dispidMessageParentMethod:144,dispidSendMessageMethod:145,dispidExecuteFeature:146,dispidQueryFeature:147,dispidMethodMax:147};OSF.DDA.EventDispId={dispidEventMin:0,dispidInitializeEvent:0,dispidSettingsChangedEvent:1,dispidDocumentSelectionChangedEvent:2,dispidBindingSelectionChangedEvent:3,dispidBindingDataChangedEvent:4,dispidDocumentOpenEvent:5,dispidDocumentCloseEvent:6,dispidActiveViewChangedEvent:7,dispidDocumentThemeChangedEvent:8,dispidOfficeThemeChangedEvent:9,dispidDialogMessageReceivedEvent:10,dispidDialogNotificationShownInAddinEvent:11,dispidDialogParentMessageReceivedEvent:12,dispidObjectDeletedEvent:13,dispidObjectSelectionChangedEvent:14,dispidObjectDataChangedEvent:15,dispidContentControlAddedEvent:16,dispidActivationStatusChangedEvent:32,dispidRichApiMessageEvent:33,dispidAppCommandInvokedEvent:39,dispidOlkItemSelectedChangedEvent:46,dispidOlkRecipientsChangedEvent:47,dispidOlkAppointmentTimeChangedEvent:48,dispidOlkRecurrenceChangedEvent:49,dispidOlkAttachmentsChangedEvent:50,dispidOlkEnhancedLocationsChangedEvent:51,dispidOlkInfobarClickedEvent:52,dispidTaskSelectionChangedEvent:56,dispidResourceSelectionChangedEvent:57,dispidViewSelectionChangedEvent:58,dispidDataNodeAddedEvent:60,dispidDataNodeReplacedEvent:61,dispidDataNodeDeletedEvent:62,dispidEventMax:63};OSF.DDA.ErrorCodeManager=function(){var a={};return {getErrorArgs:function(c){var b=a[c];if(!b)b=a[this.errorCodes.ooeInternalError];else{if(!b.name)b.name=a[this.errorCodes.ooeInternalError].name;if(!b.message)b.message=a[this.errorCodes.ooeInternalError].message}return b},addErrorMessage:function(c,b){a[c]=b},errorCodes:{ooeSuccess:0,ooeChunkResult:1,ooeCoercionTypeNotSupported:1e3,ooeGetSelectionNotMatchDataType:1001,ooeCoercionTypeNotMatchBinding:1002,ooeInvalidGetRowColumnCounts:1003,ooeSelectionNotSupportCoercionType:1004,ooeInvalidGetStartRowColumn:1005,ooeNonUniformPartialGetNotSupported:1006,ooeGetDataIsTooLarge:1008,ooeFileTypeNotSupported:1009,ooeGetDataParametersConflict:1010,ooeInvalidGetColumns:1011,ooeInvalidGetRows:1012,ooeInvalidReadForBlankRow:1013,ooeUnsupportedDataObject:2e3,ooeCannotWriteToSelection:2001,ooeDataNotMatchSelection:2002,ooeOverwriteWorksheetData:2003,ooeDataNotMatchBindingSize:2004,ooeInvalidSetStartRowColumn:2005,ooeInvalidDataFormat:2006,ooeDataNotMatchCoercionType:2007,ooeDataNotMatchBindingType:2008,ooeSetDataIsTooLarge:2009,ooeNonUniformPartialSetNotSupported:2010,ooeInvalidSetColumns:2011,ooeInvalidSetRows:2012,ooeSetDataParametersConflict:2013,ooeCellDataAmountBeyondLimits:2014,ooeSelectionCannotBound:3e3,ooeBindingNotExist:3002,ooeBindingToMultipleSelection:3003,ooeInvalidSelectionForBindingType:3004,ooeOperationNotSupportedOnThisBindingType:3005,ooeNamedItemNotFound:3006,ooeMultipleNamedItemFound:3007,ooeInvalidNamedItemForBindingType:3008,ooeUnknownBindingType:3009,ooeOperationNotSupportedOnMatrixData:3010,ooeInvalidColumnsForBinding:3011,ooeSettingNameNotExist:4e3,ooeSettingsCannotSave:4001,ooeSettingsAreStale:4002,ooeOperationNotSupported:5e3,ooeInternalError:5001,ooeDocumentReadOnly:5002,ooeEventHandlerNotExist:5003,ooeInvalidApiCallInContext:5004,ooeShuttingDown:5005,ooeUnsupportedEnumeration:5007,ooeIndexOutOfRange:5008,ooeBrowserAPINotSupported:5009,ooeInvalidParam:5010,ooeRequestTimeout:5011,ooeInvalidOrTimedOutSession:5012,ooeInvalidApiArguments:5013,ooeOperationCancelled:5014,ooeWorkbookHidden:5015,ooeWriteNotSupportedWhenModalDialogOpen:5016,ooeTooManyIncompleteRequests:5100,ooeRequestTokenUnavailable:5101,ooeActivityLimitReached:5102,ooeRequestPayloadSizeLimitExceeded:5103,ooeResponsePayloadSizeLimitExceeded:5104,ooeCustomXmlNodeNotFound:6e3,ooeCustomXmlError:6100,ooeCustomXmlExceedQuota:6101,ooeCustomXmlOutOfDate:6102,ooeNoCapability:7e3,ooeCannotNavTo:7001,ooeSpecifiedIdNotExist:7002,ooeNavOutOfBound:7004,ooeElementMissing:8e3,ooeProtectedError:8001,ooeInvalidCellsValue:8010,ooeInvalidTableOptionValue:8011,ooeInvalidFormatValue:8012,ooeRowIndexOutOfRange:8020,ooeColIndexOutOfRange:8021,ooeFormatValueOutOfRange:8022,ooeCellFormatAmountBeyondLimits:8023,ooeMemoryFileLimit:11000,ooeNetworkProblemRetrieveFile:11001,ooeInvalidSliceSize:11002,ooeInvalidCallback:11101,ooeInvalidWidth:12000,ooeInvalidHeight:12001,ooeNavigationError:12002,ooeInvalidScheme:12003,ooeAppDomains:12004,ooeRequireHTTPS:12005,ooeWebDialogClosed:12006,ooeDialogAlreadyOpened:12007,ooeEndUserAllow:12008,ooeEndUserIgnore:12009,ooeNotUILessDialog:12010,ooeCrossZone:12011,ooeModalDialogOpen:12012,ooeDocumentIsInactive:12013,ooeDialogParentIsMinimized:12014,ooeNotSSOAgave:13000,ooeSSOUserNotSignedIn:13001,ooeSSOUserAborted:13002,ooeSSOUnsupportedUserIdentity:13003,ooeSSOInvalidResourceUrl:13004,ooeSSOInvalidGrant:13005,ooeSSOClientError:13006,ooeSSOServerError:13007,ooeAddinIsAlreadyRequestingToken:13008,ooeSSOUserConsentNotSupportedByCurrentAddinCategory:13009,ooeSSOConnectionLost:13010,ooeResourceNotAllowed:13011,ooeSSOUnsupportedPlatform:13012,ooeSSOCallThrottled:13013,ooeAccessDenied:13990,ooeGeneralException:13991},initializeErrorMessages:function(b){a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCoercionTypeNotSupported]={name:b.L_InvalidCoercion,message:b.L_CoercionTypeNotSupported};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeGetSelectionNotMatchDataType]={name:b.L_DataReadError,message:b.L_GetSelectionNotSupported};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCoercionTypeNotMatchBinding]={name:b.L_InvalidCoercion,message:b.L_CoercionTypeNotMatchBinding};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidGetRowColumnCounts]={name:b.L_DataReadError,message:b.L_InvalidGetRowColumnCounts};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSelectionNotSupportCoercionType]={name:b.L_DataReadError,message:b.L_SelectionNotSupportCoercionType};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidGetStartRowColumn]={name:b.L_DataReadError,message:b.L_InvalidGetStartRowColumn};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeNonUniformPartialGetNotSupported]={name:b.L_DataReadError,message:b.L_NonUniformPartialGetNotSupported};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeGetDataIsTooLarge]={name:b.L_DataReadError,message:b.L_GetDataIsTooLarge};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeFileTypeNotSupported]={name:b.L_DataReadError,message:b.L_FileTypeNotSupported};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeGetDataParametersConflict]={name:b.L_DataReadError,message:b.L_GetDataParametersConflict};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidGetColumns]={name:b.L_DataReadError,message:b.L_InvalidGetColumns};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidGetRows]={name:b.L_DataReadError,message:b.L_InvalidGetRows};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidReadForBlankRow]={name:b.L_DataReadError,message:b.L_InvalidReadForBlankRow};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeUnsupportedDataObject]={name:b.L_DataWriteError,message:b.L_UnsupportedDataObject};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCannotWriteToSelection]={name:b.L_DataWriteError,message:b.L_CannotWriteToSelection};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeDataNotMatchSelection]={name:b.L_DataWriteError,message:b.L_DataNotMatchSelection};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeOverwriteWorksheetData]={name:b.L_DataWriteError,message:b.L_OverwriteWorksheetData};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeDataNotMatchBindingSize]={name:b.L_DataWriteError,message:b.L_DataNotMatchBindingSize};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidSetStartRowColumn]={name:b.L_DataWriteError,message:b.L_InvalidSetStartRowColumn};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidDataFormat]={name:b.L_InvalidFormat,message:b.L_InvalidDataFormat};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeDataNotMatchCoercionType]={name:b.L_InvalidDataObject,message:b.L_DataNotMatchCoercionType};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeDataNotMatchBindingType]={name:b.L_InvalidDataObject,message:b.L_DataNotMatchBindingType};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSetDataIsTooLarge]={name:b.L_DataWriteError,message:b.L_SetDataIsTooLarge};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeNonUniformPartialSetNotSupported]={name:b.L_DataWriteError,message:b.L_NonUniformPartialSetNotSupported};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidSetColumns]={name:b.L_DataWriteError,message:b.L_InvalidSetColumns};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidSetRows]={name:b.L_DataWriteError,message:b.L_InvalidSetRows};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSetDataParametersConflict]={name:b.L_DataWriteError,message:b.L_SetDataParametersConflict};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSelectionCannotBound]={name:b.L_BindingCreationError,message:b.L_SelectionCannotBound};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeBindingNotExist]={name:b.L_InvalidBindingError,message:b.L_BindingNotExist};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeBindingToMultipleSelection]={name:b.L_BindingCreationError,message:b.L_BindingToMultipleSelection};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidSelectionForBindingType]={name:b.L_BindingCreationError,message:b.L_InvalidSelectionForBindingType};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeOperationNotSupportedOnThisBindingType]={name:b.L_InvalidBindingOperation,message:b.L_OperationNotSupportedOnThisBindingType};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeNamedItemNotFound]={name:b.L_BindingCreationError,message:b.L_NamedItemNotFound};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeMultipleNamedItemFound]={name:b.L_BindingCreationError,message:b.L_MultipleNamedItemFound};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidNamedItemForBindingType]={name:b.L_BindingCreationError,message:b.L_InvalidNamedItemForBindingType};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeUnknownBindingType]={name:b.L_InvalidBinding,message:b.L_UnknownBindingType};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeOperationNotSupportedOnMatrixData]={name:b.L_InvalidBindingOperation,message:b.L_OperationNotSupportedOnMatrixData};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidColumnsForBinding]={name:b.L_InvalidBinding,message:b.L_InvalidColumnsForBinding};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSettingNameNotExist]={name:b.L_ReadSettingsError,message:b.L_SettingNameNotExist};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSettingsCannotSave]={name:b.L_SaveSettingsError,message:b.L_SettingsCannotSave};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSettingsAreStale]={name:b.L_SettingsStaleError,message:b.L_SettingsAreStale};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeOperationNotSupported]={name:b.L_HostError,message:b.L_OperationNotSupported};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError]={name:b.L_InternalError,message:b.L_InternalErrorDescription};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeDocumentReadOnly]={name:b.L_PermissionDenied,message:b.L_DocumentReadOnly};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeEventHandlerNotExist]={name:b.L_EventRegistrationError,message:b.L_EventHandlerNotExist};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidApiCallInContext]={name:b.L_InvalidAPICall,message:b.L_InvalidApiCallInContext};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeShuttingDown]={name:b.L_ShuttingDown,message:b.L_ShuttingDown};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeUnsupportedEnumeration]={name:b.L_UnsupportedEnumeration,message:b.L_UnsupportedEnumerationMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeIndexOutOfRange]={name:b.L_IndexOutOfRange,message:b.L_IndexOutOfRange};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeBrowserAPINotSupported]={name:b.L_APINotSupported,message:b.L_BrowserAPINotSupported};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeRequestTimeout]={name:b.L_APICallFailed,message:b.L_RequestTimeout};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidOrTimedOutSession]={name:b.L_InvalidOrTimedOutSession,message:b.L_InvalidOrTimedOutSessionMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidApiArguments]={name:b.L_APICallFailed,message:b.L_InvalidApiArgumentsMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeWorkbookHidden]={name:b.L_APICallFailed,message:b.L_WorkbookHiddenMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeWriteNotSupportedWhenModalDialogOpen]={name:b.L_APICallFailed,message:b.L_WriteNotSupportedWhenModalDialogOpen};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeTooManyIncompleteRequests]={name:b.L_APICallFailed,message:b.L_TooManyIncompleteRequests};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeRequestTokenUnavailable]={name:b.L_APICallFailed,message:b.L_RequestTokenUnavailable};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeActivityLimitReached]={name:b.L_APICallFailed,message:b.L_ActivityLimitReached};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeRequestPayloadSizeLimitExceeded]={name:b.L_APICallFailed,message:b.L_RequestPayloadSizeLimitExceededMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeResponsePayloadSizeLimitExceeded]={name:b.L_APICallFailed,message:b.L_ResponsePayloadSizeLimitExceededMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCustomXmlNodeNotFound]={name:b.L_InvalidNode,message:b.L_CustomXmlNodeNotFound};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCustomXmlError]={name:b.L_CustomXmlError,message:b.L_CustomXmlError};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCustomXmlExceedQuota]={name:b.L_CustomXmlExceedQuotaName,message:b.L_CustomXmlExceedQuotaMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCustomXmlOutOfDate]={name:b.L_CustomXmlOutOfDateName,message:b.L_CustomXmlOutOfDateMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeNoCapability]={name:b.L_PermissionDenied,message:b.L_NoCapability};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCannotNavTo]={name:b.L_CannotNavigateTo,message:b.L_CannotNavigateTo};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSpecifiedIdNotExist]={name:b.L_SpecifiedIdNotExist,message:b.L_SpecifiedIdNotExist};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeNavOutOfBound]={name:b.L_NavOutOfBound,message:b.L_NavOutOfBound};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCellDataAmountBeyondLimits]={name:b.L_DataWriteReminder,message:b.L_CellDataAmountBeyondLimits};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeElementMissing]={name:b.L_MissingParameter,message:b.L_ElementMissing};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeProtectedError]={name:b.L_PermissionDenied,message:b.L_NoCapability};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidCellsValue]={name:b.L_InvalidValue,message:b.L_InvalidCellsValue};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidTableOptionValue]={name:b.L_InvalidValue,message:b.L_InvalidTableOptionValue};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidFormatValue]={name:b.L_InvalidValue,message:b.L_InvalidFormatValue};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeRowIndexOutOfRange]={name:b.L_OutOfRange,message:b.L_RowIndexOutOfRange};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeColIndexOutOfRange]={name:b.L_OutOfRange,message:b.L_ColIndexOutOfRange};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeFormatValueOutOfRange]={name:b.L_OutOfRange,message:b.L_FormatValueOutOfRange};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCellFormatAmountBeyondLimits]={name:b.L_FormattingReminder,message:b.L_CellFormatAmountBeyondLimits};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeMemoryFileLimit]={name:b.L_MemoryLimit,message:b.L_CloseFileBeforeRetrieve};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeNetworkProblemRetrieveFile]={name:b.L_NetworkProblem,message:b.L_NetworkProblemRetrieveFile};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidSliceSize]={name:b.L_InvalidValue,message:b.L_SliceSizeNotSupported};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeDialogAlreadyOpened]={name:b.L_DisplayDialogError,message:b.L_DialogAlreadyOpened};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidWidth]={name:b.L_IndexOutOfRange,message:b.L_IndexOutOfRange};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidHeight]={name:b.L_IndexOutOfRange,message:b.L_IndexOutOfRange};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeNavigationError]={name:b.L_DisplayDialogError,message:b.L_NetworkProblem};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidScheme]={name:b.L_DialogNavigateError,message:b.L_DialogInvalidScheme};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeAppDomains]={name:b.L_DisplayDialogError,message:b.L_DialogAddressNotTrusted};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeRequireHTTPS]={name:b.L_DisplayDialogError,message:b.L_DialogRequireHTTPS};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeEndUserIgnore]={name:b.L_DisplayDialogError,message:b.L_UserClickIgnore};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeCrossZone]={name:b.L_DisplayDialogError,message:b.L_NewWindowCrossZoneErrorString};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeModalDialogOpen]={name:b.L_DisplayDialogError,message:b.L_ModalDialogOpen};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeDocumentIsInactive]={name:b.L_DisplayDialogError,message:b.L_DocumentIsInactive};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeDialogParentIsMinimized]={name:b.L_DisplayDialogError,message:b.L_DialogParentIsMinimized};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeNotSSOAgave]={name:b.L_APINotSupported,message:b.L_InvalidSSOAddinMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOUserNotSignedIn]={name:b.L_UserNotSignedIn,message:b.L_UserNotSignedIn};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOUserAborted]={name:b.L_UserAborted,message:b.L_UserAbortedMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOUnsupportedUserIdentity]={name:b.L_UnsupportedUserIdentity,message:b.L_UnsupportedUserIdentityMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOInvalidResourceUrl]={name:b.L_InvalidResourceUrl,message:b.L_InvalidResourceUrlMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOInvalidGrant]={name:b.L_InvalidGrant,message:b.L_InvalidGrantMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOClientError]={name:b.L_SSOClientError,message:b.L_SSOClientErrorMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOServerError]={name:b.L_SSOServerError,message:b.L_SSOServerErrorMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeAddinIsAlreadyRequestingToken]={name:b.L_AddinIsAlreadyRequestingToken,message:b.L_AddinIsAlreadyRequestingTokenMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOUserConsentNotSupportedByCurrentAddinCategory]={name:b.L_SSOUserConsentNotSupportedByCurrentAddinCategory,message:b.L_SSOUserConsentNotSupportedByCurrentAddinCategoryMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOConnectionLost]={name:b.L_SSOConnectionLostError,message:b.L_SSOConnectionLostErrorMessage};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOUnsupportedPlatform]={name:b.L_APINotSupported,message:b.L_SSOUnsupportedPlatform};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeSSOCallThrottled]={name:b.L_APICallFailed,message:b.L_RequestTokenUnavailable};a[OSF.DDA.ErrorCodeManager.errorCodes.ooeOperationCancelled]={name:b.L_OperationCancelledError,message:b.L_OperationCancelledErrorMessage}}}}();(function(a){var b;(function(b){var a=1.1,A=function(){function a(){}return a}();b.RequirementVersion=A;var d=function(){function a(b){var a=this;a.isSetSupported=function(d,b){if(d==undefined)return false;if(b==undefined)b=0;var f=this._setMap,e=f._sets;if(e.hasOwnProperty(d.toLowerCase())){var g=e[d.toLowerCase()];try{var a=this._getVersion(g);b=b+"";var c=this._getVersion(b);if(a.major>0&&a.major>c.major)return true;if(a.major>0&&a.minor>=0&&a.major==c.major&&a.minor>=c.minor)return true}catch(h){return false}}return false};a._getVersion=function(b){var a="version format incorrect";b=b+"";var c=b.split("."),d=0,e=0;if(c.length<2&&isNaN(Number(b)))throw a;else{d=Number(c[0]);if(c.length>=2)e=Number(c[1]);if(isNaN(d)||isNaN(e))throw a}var f={minor:e,major:d};return f};a._setMap=b;a.isSetSupported=a.isSetSupported.bind(a)}return a}();b.RequirementMatrix=d;var c=function(){function a(a){this._addSetMap=function(a){for(var b in a)this._sets[b]=a[b]};this._sets=a}return a}();b.DefaultSetRequirement=c;var l=function(c){__extends(b,c);function b(){return c.call(this,{dialogapi:a})||this}return b}(c);b.DefaultRequiredDialogSetRequirement=l;var k=function(c){__extends(b,c);function b(){return c.call(this,{dialogorigin:a})||this}return b}(c);b.DefaultOptionalDialogSetRequirement=k;var f=function(c){__extends(b,c);function b(){return c.call(this,{bindingevents:a,documentevents:a,excelapi:a,matrixbindings:a,matrixcoercion:a,selection:a,settings:a,tablebindings:a,tablecoercion:a,textbindings:a,textcoercion:a})||this}return b}(c);b.ExcelClientDefaultSetRequirement=f;var m=function(c){__extends(b,c);function b(){var b=c.call(this)||this;b._addSetMap({imagecoercion:a});return b}return b}(f);b.ExcelClientV1DefaultSetRequirement=m;var n=function(b){__extends(a,b);function a(){return b.call(this,{mailbox:1.3})||this}return a}(c);b.OutlookClientDefaultSetRequirement=n;var h=function(c){__extends(b,c);function b(){return c.call(this,{bindingevents:a,compressedfile:a,customxmlparts:a,documentevents:a,file:a,htmlcoercion:a,matrixbindings:a,matrixcoercion:a,ooxmlcoercion:a,pdffile:a,selection:a,settings:a,tablebindings:a,tablecoercion:a,textbindings:a,textcoercion:a,textfile:a,wordapi:a})||this}return b}(c);b.WordClientDefaultSetRequirement=h;var r=function(c){__extends(b,c);function b(){var b=c.call(this)||this;b._addSetMap({customxmlparts:1.2,wordapi:1.2,imagecoercion:a});return b}return b}(h);b.WordClientV1DefaultSetRequirement=r;var e=function(c){__extends(b,c);function b(){return c.call(this,{activeview:a,compressedfile:a,documentevents:a,file:a,pdffile:a,selection:a,settings:a,textcoercion:a})||this}return b}(c);b.PowerpointClientDefaultSetRequirement=e;var j=function(c){__extends(b,c);function b(){var b=c.call(this)||this;b._addSetMap({imagecoercion:a});return b}return b}(e);b.PowerpointClientV1DefaultSetRequirement=j;var q=function(c){__extends(b,c);function b(){return c.call(this,{selection:a,textcoercion:a})||this}return b}(c);b.ProjectClientDefaultSetRequirement=q;var w=function(c){__extends(b,c);function b(){return c.call(this,{bindingevents:a,documentevents:a,matrixbindings:a,matrixcoercion:a,selection:a,settings:a,tablebindings:a,tablecoercion:a,textbindings:a,textcoercion:a,file:a})||this}return b}(c);b.ExcelWebDefaultSetRequirement=w;var y=function(c){__extends(b,c);function b(){return c.call(this,{compressedfile:a,documentevents:a,file:a,imagecoercion:a,matrixcoercion:a,ooxmlcoercion:a,pdffile:a,selection:a,settings:a,tablecoercion:a,textcoercion:a,textfile:a})||this}return b}(c);b.WordWebDefaultSetRequirement=y;var p=function(c){__extends(b,c);function b(){return c.call(this,{activeview:a,settings:a})||this}return b}(c);b.PowerpointWebDefaultSetRequirement=p;var g=function(b){__extends(a,b);function a(){return b.call(this,{mailbox:1.3})||this}return a}(c);b.OutlookWebDefaultSetRequirement=g;var x=function(c){__extends(b,c);function b(){return c.call(this,{activeview:a,documentevents:a,selection:a,settings:a,textcoercion:a})||this}return b}(c);b.SwayWebDefaultSetRequirement=x;var t=function(c){__extends(b,c);function b(){return c.call(this,{bindingevents:a,partialtablebindings:a,settings:a,tablebindings:a,tablecoercion:a})||this}return b}(c);b.AccessWebDefaultSetRequirement=t;var v=function(c){__extends(b,c);function b(){return c.call(this,{bindingevents:a,documentevents:a,matrixbindings:a,matrixcoercion:a,selection:a,settings:a,tablebindings:a,tablecoercion:a,textbindings:a,textcoercion:a})||this}return b}(c);b.ExcelIOSDefaultSetRequirement=v;var i=function(c){__extends(b,c);function b(){return c.call(this,{bindingevents:a,compressedfile:a,customxmlparts:a,documentevents:a,file:a,htmlcoercion:a,matrixbindings:a,matrixcoercion:a,ooxmlcoercion:a,pdffile:a,selection:a,settings:a,tablebindings:a,tablecoercion:a,textbindings:a,textcoercion:a,textfile:a})||this}return b}(c);b.WordIOSDefaultSetRequirement=i;var u=function(b){__extends(a,b);function a(){var a=b.call(this)||this;a._addSetMap({customxmlparts:1.2,wordapi:1.2});return a}return a}(i);b.WordIOSV1DefaultSetRequirement=u;var o=function(c){__extends(b,c);function b(){return c.call(this,{activeview:a,compressedfile:a,documentevents:a,file:a,pdffile:a,selection:a,settings:a,textcoercion:a})||this}return b}(c);b.PowerpointIOSDefaultSetRequirement=o;var s=function(c){__extends(b,c);function b(){return c.call(this,{mailbox:a})||this}return b}(c);b.OutlookIOSDefaultSetRequirement=s;var z=function(){var b="undefined";function a(){}a.initializeOsfDda=function(){OSF.OUtil.setNamespace("Requirement",OSF.DDA)};a.getDefaultRequirementMatrix=function(f){this.initializeDefaultSetMatrix();var e=undefined,g=f.get_requirementMatrix();if(g!=undefined&&g.length>0&&typeof JSON!==b){var i=JSON.parse(f.get_requirementMatrix().toLowerCase());e=new d(new c(i))}else{var h=a.getClientFullVersionString(f);if(a.DefaultSetArrayMatrix!=undefined&&a.DefaultSetArrayMatrix[h]!=undefined)e=new d(a.DefaultSetArrayMatrix[h]);else e=new d(new c({}))}return e};a.getDefaultDialogRequirementMatrix=function(h){var a=undefined,i=h.get_dialogRequirementMatrix();if(i!=undefined&&i.length>0&&typeof JSON!==b){var f=JSON.parse(h.get_requirementMatrix().toLowerCase());a=new c(f)}else{a=new l;var g=h.get_requirementMatrix();if(g!=undefined&&g.length>0&&typeof JSON!==b){var f=JSON.parse(g.toLowerCase());for(var e in a._sets)if(f.hasOwnProperty(e))a._sets[e]=f[e];var j=new k;for(var e in j._sets)if(f.hasOwnProperty(e))a._sets[e]=f[e]}}return new d(a)};a.getClientFullVersionString=function(a){var d=a.get_appMinorVersion(),e="",b="",c=a.get_appName(),f=c==1024||c==4096||c==8192||c==65536;if(f&&a.get_appVersion()==1)if(c==4096&&d>=15)b="16.00.01";else b="16.00";else if(a.get_appName()==64)b=a.get_appVersion();else{if(d<10)e="0"+d;else e=""+d;b=a.get_appVersion()+"."+e}return a.get_appName()+"-"+b};a.initializeDefaultSetMatrix=function(){a.DefaultSetArrayMatrix[a.Excel_RCLIENT_1600]=new f;a.DefaultSetArrayMatrix[a.Word_RCLIENT_1600]=new h;a.DefaultSetArrayMatrix[a.PowerPoint_RCLIENT_1600]=new e;a.DefaultSetArrayMatrix[a.Excel_RCLIENT_1601]=new m;a.DefaultSetArrayMatrix[a.Word_RCLIENT_1601]=new r;a.DefaultSetArrayMatrix[a.PowerPoint_RCLIENT_1601]=new j;a.DefaultSetArrayMatrix[a.Outlook_RCLIENT_1600]=new n;a.DefaultSetArrayMatrix[a.Excel_WAC_1600]=new w;a.DefaultSetArrayMatrix[a.Word_WAC_1600]=new y;a.DefaultSetArrayMatrix[a.Outlook_WAC_1600]=new g;a.DefaultSetArrayMatrix[a.Outlook_WAC_1601]=new g;a.DefaultSetArrayMatrix[a.Project_RCLIENT_1600]=new q;a.DefaultSetArrayMatrix[a.Access_WAC_1600]=new t;a.DefaultSetArrayMatrix[a.PowerPoint_WAC_1600]=new p;a.DefaultSetArrayMatrix[a.Excel_IOS_1600]=new v;a.DefaultSetArrayMatrix[a.SWAY_WAC_1600]=new x;a.DefaultSetArrayMatrix[a.Word_IOS_1600]=new i;a.DefaultSetArrayMatrix[a.Word_IOS_16001]=new u;a.DefaultSetArrayMatrix[a.PowerPoint_IOS_1600]=new o;a.DefaultSetArrayMatrix[a.Outlook_IOS_1600]=new s};a.Excel_RCLIENT_1600="1-16.00";a.Excel_RCLIENT_1601="1-16.01";a.Word_RCLIENT_1600="2-16.00";a.Word_RCLIENT_1601="2-16.01";a.PowerPoint_RCLIENT_1600="4-16.00";a.PowerPoint_RCLIENT_1601="4-16.01";a.Outlook_RCLIENT_1600="8-16.00";a.Excel_WAC_1600="16-16.00";a.Word_WAC_1600="32-16.00";a.Outlook_WAC_1600="64-16.00";a.Outlook_WAC_1601="64-16.01";a.Project_RCLIENT_1600="128-16.00";a.Access_WAC_1600="256-16.00";a.PowerPoint_WAC_1600="512-16.00";a.Excel_IOS_1600="1024-16.00";a.SWAY_WAC_1600="2048-16.00";a.Word_IOS_1600="4096-16.00";a.Word_IOS_16001="4096-16.00.01";a.PowerPoint_IOS_1600="8192-16.00";a.Outlook_IOS_1600="65536-16.00";a.DefaultSetArrayMatrix={};return a}();b.RequirementsMatrixFactory=z})(b=a.Requirement||(a.Requirement={}))})(OfficeExt||(OfficeExt={}));OfficeExt.Requirement.RequirementsMatrixFactory.initializeOsfDda();Microsoft.Office.WebExtension.ApplicationMode={WebEditor:"webEditor",WebViewer:"webViewer",Client:"client"};Microsoft.Office.WebExtension.DocumentMode={ReadOnly:"readOnly",ReadWrite:"readWrite"};OSF.NamespaceManager=function(){var b,a=false;return {enableShortcut:function(){if(!a){if(window.Office)b=window.Office;else OSF.OUtil.setNamespace("Office",window);window.Office=Microsoft.Office.WebExtension;a=true}},disableShortcut:function(){if(a){if(b)window.Office=b;else OSF.OUtil.unsetNamespace("Office",window);a=false}}}}();OSF.NamespaceManager.enableShortcut();Microsoft.Office.WebExtension.useShortNamespace=function(a){if(a)OSF.NamespaceManager.enableShortcut();else OSF.NamespaceManager.disableShortcut()};Microsoft.Office.WebExtension.select=function(a,b){var c;if(a&&typeof a=="string"){var d=a.indexOf("#");if(d!=-1){var h=a.substring(0,d),g=a.substring(d+1);switch(h){case "binding":case "bindings":if(g)c=new OSF.DDA.BindingPromise(g)}}}if(!c){if(b){var e=typeof b;if(e=="function"){var f={};f[Microsoft.Office.WebExtension.Parameters.Callback]=b;OSF.DDA.issueAsyncResult(f,OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidApiCallInContext,OSF.DDA.ErrorCodeManager.getErrorArgs(OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidApiCallInContext))}else throw OSF.OUtil.formatString(Strings.OfficeOM.L_CallbackNotAFunction,e)}}else{c.onFail=b;return c}};OSF.DDA.Context=function(a,h,i,c,d){var g="officeTheme",f="requirements",b=this;OSF.OUtil.defineEnumerableProperties(b,{contentLanguage:{value:a.get_dataLocale()},displayLanguage:{value:a.get_appUILocale()},touchEnabled:{value:a.get_touchEnabled()},commerceAllowed:{value:a.get_commerceAllowed()},host:{value:OfficeExt.HostName.Host.getInstance().getHost()},platform:{value:OfficeExt.HostName.Host.getInstance().getPlatform()},isDialog:{value:OSF._OfficeAppFactory.getHostInfo().isDialog},diagnostics:{value:OfficeExt.HostName.Host.getInstance().getDiagnostics(a.get_hostFullVersion())}});i&&OSF.OUtil.defineEnumerableProperty(b,"license",{value:i});a.ui&&OSF.OUtil.defineEnumerableProperty(b,"ui",{value:a.ui});a.auth&&OSF.OUtil.defineEnumerableProperty(b,"auth",{value:a.auth});a.webAuth&&OSF.OUtil.defineEnumerableProperty(b,"webAuth",{value:a.webAuth});a.application&&OSF.OUtil.defineEnumerableProperty(b,"application",{value:a.application});a.extensionLifeCycle&&OSF.OUtil.defineEnumerableProperty(b,"extensionLifeCycle",{value:a.extensionLifeCycle});a.messaging&&OSF.OUtil.defineEnumerableProperty(b,"messaging",{value:a.messaging});a.ui&&a.ui.taskPaneAction&&OSF.OUtil.defineEnumerableProperty(b,"taskPaneAction",{value:a.ui.taskPaneAction});a.ui&&a.ui.ribbonGallery&&OSF.OUtil.defineEnumerableProperty(b,"ribbonGallery",{value:a.ui.ribbonGallery});if(a.get_isDialog()){var e=OfficeExt.Requirement.RequirementsMatrixFactory.getDefaultDialogRequirementMatrix(a);OSF.OUtil.defineEnumerableProperty(b,f,{value:e})}else{h&&OSF.OUtil.defineEnumerableProperty(b,"document",{value:h});if(c){var j=c.displayName||"appOM";delete c.displayName;OSF.OUtil.defineEnumerableProperty(b,j,{value:c})}if(a.get_officeTheme())OSF.OUtil.defineEnumerableProperty(b,g,{"get":function(){return a.get_officeTheme()}});else d&&OSF.OUtil.defineEnumerableProperty(b,g,{"get":function(){return d()}});var e=OfficeExt.Requirement.RequirementsMatrixFactory.getDefaultRequirementMatrix(a);OSF.OUtil.defineEnumerableProperty(b,f,{value:e})}};OSF.DDA.OutlookContext=function(c,a,d,e,b){OSF.DDA.OutlookContext.uber.constructor.call(this,c,null,d,e,b);a&&OSF.OUtil.defineEnumerableProperty(this,"roamingSettings",{value:a})};OSF.OUtil.extend(OSF.DDA.OutlookContext,OSF.DDA.Context);OSF.DDA.OutlookAppOm=function(){};OSF.DDA.Application=function(){};OSF.DDA.Document=function(b,c){var a;switch(b.get_clientMode()){case OSF.ClientMode.ReadOnly:a=Microsoft.Office.WebExtension.DocumentMode.ReadOnly;break;case OSF.ClientMode.ReadWrite:a=Microsoft.Office.WebExtension.DocumentMode.ReadWrite}c&&OSF.OUtil.defineEnumerableProperty(this,"settings",{value:c});OSF.OUtil.defineMutableProperties(this,{mode:{value:a},url:{value:b.get_docUrl()}})};OSF.DDA.JsomDocument=function(d,b,e){var a=this;OSF.DDA.JsomDocument.uber.constructor.call(a,d,e);b&&OSF.OUtil.defineEnumerableProperty(a,"bindings",{"get":function(){return b}});var c=OSF.DDA.AsyncMethodNames;OSF.DDA.DispIdHost.addAsyncMethods(a,[c.GetSelectedDataAsync,c.SetSelectedDataAsync]);OSF.DDA.DispIdHost.addEventSupport(a,new OSF.EventDispatch([Microsoft.Office.WebExtension.EventType.DocumentSelectionChanged]))};OSF.OUtil.extend(OSF.DDA.JsomDocument,OSF.DDA.Document);OSF.OUtil.defineEnumerableProperty(Microsoft.Office.WebExtension,"context",{"get":function(){var a;if(OSF&&OSF._OfficeAppFactory)a=OSF._OfficeAppFactory.getContext();return a}});OSF.DDA.License=function(a){OSF.OUtil.defineEnumerableProperty(this,"value",{value:a})};OSF.DDA.ApiMethodCall=function(c,f,e,g,h){var a=this,d=c.length,b=OSF.OUtil.delayExecutionAndCache(function(){return OSF.OUtil.formatString(Strings.OfficeOM.L_InvalidParameters,h)});a.verifyArguments=function(d,f){for(var e in d){var a=d[e],c=f[e];if(a["enum"])switch(typeof c){case "string":if(OSF.OUtil.listContainsValue(a["enum"],c))break;case "undefined":throw OSF.DDA.ErrorCodeManager.errorCodes.ooeUnsupportedEnumeration;default:throw b()}if(a["types"])if(!OSF.OUtil.listContainsValue(a["types"],typeof c))throw b()}};a.extractRequiredArguments=function(g,l,j){if(g.length<d)throw OsfMsAjaxFactory.msAjaxError.parameterCount(Strings.OfficeOM.L_MissingRequiredArguments);for(var e=[],a=0;a<d;a++)e.push(g[a]);this.verifyArguments(c,e);var i={};for(a=0;a<d;a++){var f=c[a],h=e[a];if(f.verify){var k=f.verify(h,l,j);if(!k)throw b()}i[f.name]=h}return i},a.fillOptions=function(a,e,h,g){a=a||{};for(var d in f)if(!OSF.OUtil.listContainsKey(a,d)){var c=undefined,b=f[d];if(b.calculate&&e)c=b.calculate(e,h,g);if(!c&&b.defaultValue!==undefined)c=b.defaultValue;a[d]=c}return a};a.constructCallArgs=function(c,d,f,b){var a={};for(var i in c)a[i]=c[i];for(var h in d)a[h]=d[h];for(var j in e)a[j]=e[j](f,b);if(g)a=g(a,f,b);return a}};OSF.OUtil.setNamespace("AsyncResultEnum",OSF.DDA);OSF.DDA.AsyncResultEnum.Properties={Context:"Context",Value:"Value",Status:"Status",Error:"Error"};Microsoft.Office.WebExtension.AsyncResultStatus={Succeeded:"succeeded",Failed:"failed"};OSF.DDA.AsyncResultEnum.ErrorCode={Success:0,Failed:1};OSF.DDA.AsyncResultEnum.ErrorProperties={Name:"Name",Message:"Message",Code:"Code"};OSF.DDA.AsyncMethodNames={};OSF.DDA.AsyncMethodNames.addNames=function(b){for(var a in b){var c={};OSF.OUtil.defineEnumerableProperties(c,{id:{value:a},displayName:{value:b[a]}});OSF.DDA.AsyncMethodNames[a]=c}};OSF.DDA.AsyncMethodCall=function(d,e,i,f,g,j,k){var a="function",c=d.length,b=new OSF.DDA.ApiMethodCall(d,e,i,j,k);function h(h,j,l,k){if(h.length>c+2)throw OsfMsAjaxFactory.msAjaxError.parameterCount(Strings.OfficeOM.L_TooManyArguments);for(var d,f,i=h.length-1;i>=c;i--){var g=h[i];switch(typeof g){case "object":if(d)throw OsfMsAjaxFactory.msAjaxError.parameterCount(Strings.OfficeOM.L_TooManyOptionalObjects);else d=g;break;case a:if(f)throw OsfMsAjaxFactory.msAjaxError.parameterCount(Strings.OfficeOM.L_TooManyOptionalFunction);else f=g;break;default:throw OsfMsAjaxFactory.msAjaxError.argument(Strings.OfficeOM.L_InValidOptionalArgument)}}d=b.fillOptions(d,j,l,k);if(f)if(d[Microsoft.Office.WebExtension.Parameters.Callback])throw Strings.OfficeOM.L_RedundantCallbackSpecification;else d[Microsoft.Office.WebExtension.Parameters.Callback]=f;b.verifyArguments(e,d);return d}this.verifyAndExtractCall=function(e,c,a){var d=b.extractRequiredArguments(e,c,a),g=h(e,d,c,a),f=b.constructCallArgs(d,g,c,a);return f};this.processResponse=function(c,b,e,d){var a;if(c==OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess)if(f)a=f(b,e,d);else a=b;else if(g)a=g(c,b);else a=OSF.DDA.ErrorCodeManager.getErrorArgs(c);return a};this.getCallArgs=function(g){for(var b,d,f=g.length-1;f>=c;f--){var e=g[f];switch(typeof e){case "object":b=e;break;case a:d=e}}b=b||{};if(d)b[Microsoft.Office.WebExtension.Parameters.Callback]=d;return b}};OSF.DDA.AsyncMethodCallFactory=function(){return {manufacture:function(a){var c=a.supportedOptions?OSF.OUtil.createObject(a.supportedOptions):[],b=a.privateStateCallbacks?OSF.OUtil.createObject(a.privateStateCallbacks):[];return new OSF.DDA.AsyncMethodCall(a.requiredArguments||[],c,b,a.onSucceeded,a.onFailed,a.checkCallArgs,a.method.displayName)}}}();OSF.DDA.AsyncMethodCalls={};OSF.DDA.AsyncMethodCalls.define=function(a){OSF.DDA.AsyncMethodCalls[a.method.id]=OSF.DDA.AsyncMethodCallFactory.manufacture(a)};OSF.DDA.Error=function(c,a,b){OSF.OUtil.defineEnumerableProperties(this,{name:{value:c},message:{value:a},code:{value:b}})};OSF.DDA.AsyncResult=function(b,a){OSF.OUtil.defineEnumerableProperties(this,{value:{value:b[OSF.DDA.AsyncResultEnum.Properties.Value]},status:{value:a?Microsoft.Office.WebExtension.AsyncResultStatus.Failed:Microsoft.Office.WebExtension.AsyncResultStatus.Succeeded}});b[OSF.DDA.AsyncResultEnum.Properties.Context]&&OSF.OUtil.defineEnumerableProperty(this,"asyncContext",{value:b[OSF.DDA.AsyncResultEnum.Properties.Context]});a&&OSF.OUtil.defineEnumerableProperty(this,"error",{value:new OSF.DDA.Error(a[OSF.DDA.AsyncResultEnum.ErrorProperties.Name],a[OSF.DDA.AsyncResultEnum.ErrorProperties.Message],a[OSF.DDA.AsyncResultEnum.ErrorProperties.Code])})};OSF.DDA.issueAsyncResult=function(d,f,a){var e=d[Microsoft.Office.WebExtension.Parameters.Callback];if(e){var c={};c[OSF.DDA.AsyncResultEnum.Properties.Context]=d[Microsoft.Office.WebExtension.Parameters.AsyncContext];var b;if(f==OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess)c[OSF.DDA.AsyncResultEnum.Properties.Value]=a;else{b={};a=a||OSF.DDA.ErrorCodeManager.getErrorArgs(OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError);b[OSF.DDA.AsyncResultEnum.ErrorProperties.Code]=f||OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError;b[OSF.DDA.AsyncResultEnum.ErrorProperties.Name]=a.name||a;b[OSF.DDA.AsyncResultEnum.ErrorProperties.Message]=a.message||a}e(new OSF.DDA.AsyncResult(c,b))}};OSF.DDA.SyncMethodNames={};OSF.DDA.SyncMethodNames.addNames=function(b){for(var a in b){var c={};OSF.OUtil.defineEnumerableProperties(c,{id:{value:a},displayName:{value:b[a]}});OSF.DDA.SyncMethodNames[a]=c}};OSF.DDA.SyncMethodCall=function(b,c,f,g,h){var d=b.length,a=new OSF.DDA.ApiMethodCall(b,c,f,g,h);function e(e,h,j,i){if(e.length>d+1)throw OsfMsAjaxFactory.msAjaxError.parameterCount(Strings.OfficeOM.L_TooManyArguments);for(var b,k,f=e.length-1;f>=d;f--){var g=e[f];switch(typeof g){case "object":if(b)throw OsfMsAjaxFactory.msAjaxError.parameterCount(Strings.OfficeOM.L_TooManyOptionalObjects);else b=g;break;default:throw OsfMsAjaxFactory.msAjaxError.argument(Strings.OfficeOM.L_InValidOptionalArgument)}}b=a.fillOptions(b,h,j,i);a.verifyArguments(c,b);return b}this.verifyAndExtractCall=function(f,c,b){var d=a.extractRequiredArguments(f,c,b),h=e(f,d,c,b),g=a.constructCallArgs(d,h,c,b);return g}};OSF.DDA.SyncMethodCallFactory=function(){return {manufacture:function(a){var b=a.supportedOptions?OSF.OUtil.createObject(a.supportedOptions):[];return new OSF.DDA.SyncMethodCall(a.requiredArguments||[],b,a.privateStateCallbacks,a.checkCallArgs,a.method.displayName)}}}();OSF.DDA.SyncMethodCalls={};OSF.DDA.SyncMethodCalls.define=function(a){OSF.DDA.SyncMethodCalls[a.method.id]=OSF.DDA.SyncMethodCallFactory.manufacture(a)};OSF.DDA.ListType=function(){var a={};return {setListType:function(c,b){a[c]=b},isListType:function(b){return OSF.OUtil.listContainsKey(a,b)},getDescriptor:function(b){return a[b]}}}();OSF.DDA.HostParameterMap=function(b,c){var j="fromHost",a=this,i="toHost",e=j,l="sourceData",g="self",d={};d[Microsoft.Office.WebExtension.Parameters.Data]={toHost:function(a){if(a!=null&&a.rows!==undefined){var b={};b[OSF.DDA.TableDataProperties.TableRows]=a.rows;b[OSF.DDA.TableDataProperties.TableHeaders]=a.headers;a=b}return a},fromHost:function(a){return a}};d[Microsoft.Office.WebExtension.Parameters.SampleData]=d[Microsoft.Office.WebExtension.Parameters.Data];function f(j,i){var m=j?{}:undefined;for(var h in j){var g=j[h],a;if(OSF.DDA.ListType.isListType(h)){a=[];for(var n in g)a.push(f(g[n],i))}else if(OSF.OUtil.listContainsKey(d,h))a=d[h][i](g);else if(i==e&&b.preserveNesting(h))a=f(g,i);else{var k=c[h];if(k){var l=k[i];if(l){a=l[g];if(a===undefined)a=g}}else a=g}m[h]=a}return m}function k(j,h){var e;for(var a in h){var d;if(b.isComplexType(a))d=k(j,c[a][i]);else d=j[a];if(d!=undefined){if(!e)e={};var f=h[a];if(f==g)f=a;e[f]=b.pack(a,d)}}return e}function h(j,n,f){if(!f)f={};for(var a in n){var k=n[a],d;if(k==g)d=j;else if(k==l){f[a]=j.toArray();continue}else d=j[k];if(d===null||d===undefined)f[a]=undefined;else{d=b.unpack(a,d);var i;if(b.isComplexType(a)){i=c[a][e];if(b.preserveNesting(a))f[a]=h(d,i);else h(d,i,f)}else if(OSF.DDA.ListType.isListType(a)){i={};var p=OSF.DDA.ListType.getDescriptor(a);i[p]=g;var m=new Array(d.length);for(var o in d)m[o]=h(d[o],i);f[a]=m}else f[a]=d}}return f}function m(l,e,a){var d=c[l][a],b;if(a=="toHost"){var i=f(e,a);b=k(i,d)}else if(a==j){var g=h(e,d);b=f(g,a)}return b}if(!c)c={};a.addMapping=function(l,h){var a,d;if(h.map){a=h.map;d={};for(var j in a){var k=a[j];if(k==g)k=j;d[k]=j}}else{a=h.toHost;d=h.fromHost}var b=c[l];if(b){var f=b[i];for(var n in f)a[n]=f[n];f=b[e];for(var m in f)d[m]=f[m]}else b=c[l]={};b[i]=a;b[e]=d};a.toHost=function(b,a){return m(b,a,i)};a.fromHost=function(a,b){return m(a,b,e)};a.self=g;a.sourceData=l;a.addComplexType=function(a){b.addComplexType(a)};a.getDynamicType=function(a){return b.getDynamicType(a)};a.setDynamicType=function(c,a){b.setDynamicType(c,a)};a.dynamicTypes=d;a.doMapValues=function(a,b){return f(a,b)}};OSF.DDA.SpecialProcessor=function(c,b){var a=this;a.addComplexType=function(a){c.push(a)};a.getDynamicType=function(a){return b[a]};a.setDynamicType=function(c,a){b[c]=a};a.isComplexType=function(a){return OSF.OUtil.listContainsValue(c,a)};a.isDynamicType=function(a){return OSF.OUtil.listContainsKey(b,a)};a.preserveNesting=function(b){var a=[];OSF.DDA.PropertyDescriptors&&a.push(OSF.DDA.PropertyDescriptors.Subset);if(OSF.DDA.DataNodeEventProperties)a=a.concat([OSF.DDA.DataNodeEventProperties.OldNode,OSF.DDA.DataNodeEventProperties.NewNode,OSF.DDA.DataNodeEventProperties.NextSiblingNode]);return OSF.OUtil.listContainsValue(a,b)};a.pack=function(c,d){var a;if(this.isDynamicType(c))a=b[c].toHost(d);else a=d;return a};a.unpack=function(c,d){var a;if(this.isDynamicType(c))a=b[c].fromHost(d);else a=d;return a}};OSF.DDA.getDecoratedParameterMap=function(d,c){var a=new OSF.DDA.HostParameterMap(d),f=a.self;function b(a){var c=null;if(a){c={};for(var d=a.length,b=0;b<d;b++)c[a[b].name]=a[b].value}return c}a.define=function(c){var d={},e=b(c.toHost);if(c.invertible)d.map=e;else if(c.canonical)d.toHost=d.fromHost=e;else{d.toHost=e;d.fromHost=b(c.fromHost)}a.addMapping(c.type,d);c.isComplexType&&a.addComplexType(c.type)};for(var e in c)a.define(c[e]);return a};OSF.OUtil.setNamespace("DispIdHost",OSF.DDA);OSF.DDA.DispIdHost.Methods={InvokeMethod:"invokeMethod",AddEventHandler:"addEventHandler",RemoveEventHandler:"removeEventHandler",OpenDialog:"openDialog",CloseDialog:"closeDialog",MessageParent:"messageParent",SendMessage:"sendMessage"};OSF.DDA.DispIdHost.Delegates={ExecuteAsync:"executeAsync",RegisterEventAsync:"registerEventAsync",UnregisterEventAsync:"unregisterEventAsync",ParameterMap:"parameterMap",OpenDialog:"openDialog",CloseDialog:"closeDialog",MessageParent:"messageParent",SendMessage:"sendMessage"};OSF.DDA.DispIdHost.Facade=function(f,h){var c=false,b=null,g=this,d={},e=OSF.DDA.AsyncMethodNames,a=OSF.DDA.MethodDispId,n={GoToByIdAsync:a.dispidNavigateToMethod,GetSelectedDataAsync:a.dispidGetSelectedDataMethod,SetSelectedDataAsync:a.dispidSetSelectedDataMethod,GetDocumentCopyChunkAsync:a.dispidGetDocumentCopyChunkMethod,ReleaseDocumentCopyAsync:a.dispidReleaseDocumentCopyMethod,GetDocumentCopyAsync:a.dispidGetDocumentCopyMethod,AddFromSelectionAsync:a.dispidAddBindingFromSelectionMethod,AddFromPromptAsync:a.dispidAddBindingFromPromptMethod,AddFromNamedItemAsync:a.dispidAddBindingFromNamedItemMethod,GetAllAsync:a.dispidGetAllBindingsMethod,GetByIdAsync:a.dispidGetBindingMethod,ReleaseByIdAsync:a.dispidReleaseBindingMethod,GetDataAsync:a.dispidGetBindingDataMethod,SetDataAsync:a.dispidSetBindingDataMethod,AddRowsAsync:a.dispidAddRowsMethod,AddColumnsAsync:a.dispidAddColumnsMethod,DeleteAllDataValuesAsync:a.dispidClearAllRowsMethod,RefreshAsync:a.dispidLoadSettingsMethod,SaveAsync:a.dispidSaveSettingsMethod,GetActiveViewAsync:a.dispidGetActiveViewMethod,GetFilePropertiesAsync:a.dispidGetFilePropertiesMethod,GetOfficeThemeAsync:a.dispidGetOfficeThemeMethod,GetDocumentThemeAsync:a.dispidGetDocumentThemeMethod,ClearFormatsAsync:a.dispidClearFormatsMethod,SetTableOptionsAsync:a.dispidSetTableOptionsMethod,SetFormatsAsync:a.dispidSetFormatsMethod,GetAccessTokenAsync:a.dispidGetAccessTokenMethod,GetAuthContextAsync:a.dispidGetAuthContextMethod,ExecuteRichApiRequestAsync:a.dispidExecuteRichApiRequestMethod,AppCommandInvocationCompletedAsync:a.dispidAppCommandInvocationCompletedMethod,CloseContainerAsync:a.dispidCloseContainerMethod,OpenBrowserWindow:a.dispidOpenBrowserWindow,CreateDocumentAsync:a.dispidCreateDocumentMethod,InsertFormAsync:a.dispidInsertFormMethod,ExecuteFeature:a.dispidExecuteFeature,QueryFeature:a.dispidQueryFeature,AddDataPartAsync:a.dispidAddDataPartMethod,GetDataPartByIdAsync:a.dispidGetDataPartByIdMethod,GetDataPartsByNameSpaceAsync:a.dispidGetDataPartsByNamespaceMethod,GetPartXmlAsync:a.dispidGetDataPartXmlMethod,GetPartNodesAsync:a.dispidGetDataPartNodesMethod,DeleteDataPartAsync:a.dispidDeleteDataPartMethod,GetNodeValueAsync:a.dispidGetDataNodeValueMethod,GetNodeXmlAsync:a.dispidGetDataNodeXmlMethod,GetRelativeNodesAsync:a.dispidGetDataNodesMethod,SetNodeValueAsync:a.dispidSetDataNodeValueMethod,SetNodeXmlAsync:a.dispidSetDataNodeXmlMethod,AddDataPartNamespaceAsync:a.dispidAddDataNamespaceMethod,GetDataPartNamespaceAsync:a.dispidGetDataUriByPrefixMethod,GetDataPartPrefixAsync:a.dispidGetDataPrefixByUriMethod,GetNodeTextAsync:a.dispidGetDataNodeTextMethod,SetNodeTextAsync:a.dispidSetDataNodeTextMethod,GetSelectedTask:a.dispidGetSelectedTaskMethod,GetTask:a.dispidGetTaskMethod,GetWSSUrl:a.dispidGetWSSUrlMethod,GetTaskField:a.dispidGetTaskFieldMethod,GetSelectedResource:a.dispidGetSelectedResourceMethod,GetResourceField:a.dispidGetResourceFieldMethod,GetProjectField:a.dispidGetProjectFieldMethod,GetSelectedView:a.dispidGetSelectedViewMethod,GetTaskByIndex:a.dispidGetTaskByIndexMethod,GetResourceByIndex:a.dispidGetResourceByIndexMethod,SetTaskField:a.dispidSetTaskFieldMethod,SetResourceField:a.dispidSetResourceFieldMethod,GetMaxTaskIndex:a.dispidGetMaxTaskIndexMethod,GetMaxResourceIndex:a.dispidGetMaxResourceIndexMethod,CreateTask:a.dispidCreateTaskMethod};for(var i in n)if(e[i])d[e[i].id]=n[i];e=OSF.DDA.SyncMethodNames;a=OSF.DDA.MethodDispId;var m={MessageParent:a.dispidMessageParentMethod,SendMessage:a.dispidSendMessageMethod};for(var i in m)if(e[i])d[e[i].id]=m[i];e=Microsoft.Office.WebExtension.EventType;a=OSF.DDA.EventDispId;var o={SettingsChanged:a.dispidSettingsChangedEvent,DocumentSelectionChanged:a.dispidDocumentSelectionChangedEvent,BindingSelectionChanged:a.dispidBindingSelectionChangedEvent,BindingDataChanged:a.dispidBindingDataChangedEvent,ActiveViewChanged:a.dispidActiveViewChangedEvent,OfficeThemeChanged:a.dispidOfficeThemeChangedEvent,DocumentThemeChanged:a.dispidDocumentThemeChangedEvent,AppCommandInvoked:a.dispidAppCommandInvokedEvent,DialogMessageReceived:a.dispidDialogMessageReceivedEvent,DialogParentMessageReceived:a.dispidDialogParentMessageReceivedEvent,ObjectDeleted:a.dispidObjectDeletedEvent,ObjectSelectionChanged:a.dispidObjectSelectionChangedEvent,ObjectDataChanged:a.dispidObjectDataChangedEvent,ContentControlAdded:a.dispidContentControlAddedEvent,RichApiMessage:a.dispidRichApiMessageEvent,ItemChanged:a.dispidOlkItemSelectedChangedEvent,RecipientsChanged:a.dispidOlkRecipientsChangedEvent,AppointmentTimeChanged:a.dispidOlkAppointmentTimeChangedEvent,RecurrenceChanged:a.dispidOlkRecurrenceChangedEvent,AttachmentsChanged:a.dispidOlkAttachmentsChangedEvent,EnhancedLocationsChanged:a.dispidOlkEnhancedLocationsChangedEvent,InfobarClicked:a.dispidOlkInfobarClickedEvent,TaskSelectionChanged:a.dispidTaskSelectionChangedEvent,ResourceSelectionChanged:a.dispidResourceSelectionChangedEvent,ViewSelectionChanged:a.dispidViewSelectionChangedEvent,DataNodeInserted:a.dispidDataNodeAddedEvent,DataNodeReplaced:a.dispidDataNodeReplacedEvent,DataNodeDeleted:a.dispidDataNodeDeletedEvent};for(var k in o)if(e[k])d[e[k]]=o[k];function l(a){return a==OSF.DDA.EventDispId.dispidObjectDeletedEvent||a==OSF.DDA.EventDispId.dispidObjectSelectionChangedEvent||a==OSF.DDA.EventDispId.dispidObjectDataChangedEvent||a==OSF.DDA.EventDispId.dispidContentControlAddedEvent}function j(a,c,d,b){if(typeof a=="number"){if(!b)b=c.getCallArgs(d);OSF.DDA.issueAsyncResult(b,a,OSF.DDA.ErrorCodeManager.getErrorArgs(a))}else throw a}g[OSF.DDA.DispIdHost.Methods.InvokeMethod]=function(t,m,n,q){var a;try{var i=t.id,l=OSF.DDA.AsyncMethodCalls[i];a=l.verifyAndExtractCall(m,n,q);var k=d[i],s=f(i),c=b;if(window.Excel&&window.Office.context.requirements.isSetSupported("RedirectV1Api"))window.Excel._RedirectV1APIs=true;if(window.Excel&&window.Excel._RedirectV1APIs&&(c=window.Excel._V1APIMap[i])){var e=OSF.OUtil.shallowCopy(a);delete e[Microsoft.Office.WebExtension.Parameters.AsyncContext];if(c.preprocess)e=c.preprocess(e);var o=new window.Excel.RequestContext,u=c.call(o,e);o.sync().then(function(){var b=u.value,d=b.status;delete b["status"];delete b["@odata.type"];if(c.postprocess)b=c.postprocess(b,e);if(d!=0)b=OSF.DDA.ErrorCodeManager.getErrorArgs(d);OSF.DDA.issueAsyncResult(a,d,b)})["catch"](function(){OSF.DDA.issueAsyncResult(a,OSF.DDA.ErrorCodeManager.errorCodes.ooeFailure,b)})}else{var g;if(h.toHost)g=h.toHost(k,a);else g=a;var r=(new Date).getTime();s[OSF.DDA.DispIdHost.Delegates.ExecuteAsync]({dispId:k,hostCallArgs:g,onCalling:function(){},onReceiving:function(){},onComplete:function(c,d){var b;if(c==OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess)if(h.fromHost)b=h.fromHost(k,d);else b=d;else b=d;var e=l.processResponse(c,b,n,a);OSF.DDA.issueAsyncResult(a,c,e);OSF.AppTelemetry&&!(OSF.ConstantNames&&OSF.ConstantNames.IsCustomFunctionsRuntime)&&OSF.AppTelemetry.onMethodDone(k,g,Math.abs((new Date).getTime()-r),c)}})}}catch(p){j(p,l,m,a)}};g[OSF.DDA.DispIdHost.Methods.AddEventHandler]=function(p,b,o,s){var e,a,n,g=c;function k(c){if(c==OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess){var f=!g?b.addEventHandler(a,n):b.addObjectEventHandler(a,e[Microsoft.Office.WebExtension.Parameters.Id],n);if(!f)c=OSF.DDA.ErrorCodeManager.errorCodes.ooeEventHandlerAdditionFailed}var d;if(c!=OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess)d=OSF.DDA.ErrorCodeManager.getErrorArgs(c);OSF.DDA.issueAsyncResult(e,c,d)}try{var q=OSF.DDA.AsyncMethodCalls[OSF.DDA.AsyncMethodNames.AddHandlerAsync.id];e=q.verifyAndExtractCall(p,o,b);a=e[Microsoft.Office.WebExtension.Parameters.EventType];n=e[Microsoft.Office.WebExtension.Parameters.Handler];if(s){k(OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess);return}var m=d[a];g=l(m);var i=g?e[Microsoft.Office.WebExtension.Parameters.Id]:o.id||"",u=g?b.getObjectEventHandlerCount(a,i):b.getEventHandlerCount(a);if(u==0){var t=f(a)[OSF.DDA.DispIdHost.Delegates.RegisterEventAsync];t({eventType:a,dispId:m,targetId:i,onCalling:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.IssueCall)},onReceiving:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.ReceiveResponse)},onComplete:k,onEvent:function(d){var c=h.fromHost(m,d);if(!g)b.fireEvent(OSF.DDA.OMFactory.manufactureEventArgs(a,o,c));else b.fireObjectEvent(i,OSF.DDA.OMFactory.manufactureEventArgs(a,i,c))}})}else k(OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess)}catch(r){j(r,q,p,e)}};g[OSF.DDA.DispIdHost.Methods.RemoveEventHandler]=function(p,e,r){var g,a,m,h=c;function o(a){var b;if(a!=OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess)b=OSF.DDA.ErrorCodeManager.getErrorArgs(a);OSF.DDA.issueAsyncResult(g,a,b)}try{var q=OSF.DDA.AsyncMethodCalls[OSF.DDA.AsyncMethodNames.RemoveHandlerAsync.id];g=q.verifyAndExtractCall(p,r,e);a=g[Microsoft.Office.WebExtension.Parameters.EventType];m=g[Microsoft.Office.WebExtension.Parameters.Handler];var s=d[a];h=l(s);var k=h?g[Microsoft.Office.WebExtension.Parameters.Id]:r.id||"",n,i;if(m===b){i=h?e.clearObjectEventHandlers(a,k):e.clearEventHandlers(a);n=OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess}else{i=h?e.removeObjectEventHandler(a,k,m):e.removeEventHandler(a,m);n=i?OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess:OSF.DDA.ErrorCodeManager.errorCodes.ooeEventHandlerNotExist}var v=h?e.getObjectEventHandlerCount(a,k):e.getEventHandlerCount(a);if(i&&v==0){var u=f(a)[OSF.DDA.DispIdHost.Delegates.UnregisterEventAsync];u({eventType:a,dispId:s,targetId:k,onCalling:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.IssueCall)},onReceiving:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.ReceiveResponse)},onComplete:o})}else o(n)}catch(t){j(t,q,p,g)}};g[OSF.DDA.DispIdHost.Methods.OpenDialog]=function(p,a,o,q){var g,n,k=b,e=Microsoft.Office.WebExtension.EventType.DialogMessageReceived,i=Microsoft.Office.WebExtension.EventType.DialogEventReceived;function l(b){var d;if(b!=OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess)d=OSF.DDA.ErrorCodeManager.getErrorArgs(b);else{var c={};c[Microsoft.Office.WebExtension.Parameters.Id]=n;c[Microsoft.Office.WebExtension.Parameters.Data]=a;var d=k.processResponse(b,c,o,g);OSF.DialogShownStatus.hasDialogShown=true;a.clearEventHandlers(e);a.clearEventHandlers(i)}OSF.DDA.issueAsyncResult(g,b,d)}try{(e==undefined||i==undefined)&&l(OSF.DDA.ErrorCodeManager.ooeOperationNotSupported);if(!q){if(OSF.DDA.AsyncMethodNames.DisplayDialogAsync==b){l(OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError);return}k=OSF.DDA.AsyncMethodCalls[OSF.DDA.AsyncMethodNames.DisplayDialogAsync.id]}else{if(OSF.DDA.AsyncMethodNames.DisplayModalDialogAsync==b){l(OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError);return}k=OSF.DDA.AsyncMethodCalls[OSF.DDA.AsyncMethodNames.DisplayModalDialogAsync.id]}g=k.verifyAndExtractCall(p,o,a);var r=d[e],m=f(e),t=m[OSF.DDA.DispIdHost.Delegates.OpenDialog]!=undefined?m[OSF.DDA.DispIdHost.Delegates.OpenDialog]:m[OSF.DDA.DispIdHost.Delegates.RegisterEventAsync];g["isModal"]=q;n=JSON.stringify(g);if(!OSF.DialogShownStatus.hasDialogShown){a.clearQueuedEvent(e);a.clearQueuedEvent(i);a.clearQueuedEvent(Microsoft.Office.WebExtension.EventType.DialogParentMessageReceived)}t({eventType:e,dispId:r,targetId:n,onCalling:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.IssueCall)},onReceiving:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.ReceiveResponse)},onComplete:l,onEvent:function(j){var g=h.fromHost(r,j),f=OSF.DDA.OMFactory.manufactureEventArgs(e,o,g);if(f.type==i){var d=OSF.DDA.ErrorCodeManager.getErrorArgs(f.error),b={};b[OSF.DDA.AsyncResultEnum.ErrorProperties.Code]=status||OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError;b[OSF.DDA.AsyncResultEnum.ErrorProperties.Name]=d.name||d;b[OSF.DDA.AsyncResultEnum.ErrorProperties.Message]=d.message||d;f.error=new OSF.DDA.Error(b[OSF.DDA.AsyncResultEnum.ErrorProperties.Name],b[OSF.DDA.AsyncResultEnum.ErrorProperties.Message],b[OSF.DDA.AsyncResultEnum.ErrorProperties.Code])}a.fireOrQueueEvent(f);if(g[OSF.DDA.PropertyDescriptors.MessageType]==OSF.DialogMessageType.DialogClosed){a.clearEventHandlers(e);a.clearEventHandlers(i);a.clearEventHandlers(Microsoft.Office.WebExtension.EventType.DialogParentMessageReceived);OSF.DialogShownStatus.hasDialogShown=c}}})}catch(s){j(s,k,p,g)}};g[OSF.DDA.DispIdHost.Methods.CloseDialog]=function(h,o,e,q){var l,a,i,g=OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess;function n(a){g=a;OSF.DialogShownStatus.hasDialogShown=c}try{var k=OSF.DDA.AsyncMethodCalls[OSF.DDA.AsyncMethodNames.CloseAsync.id];l=k.verifyAndExtractCall(h,q,e);a=Microsoft.Office.WebExtension.EventType.DialogMessageReceived;i=Microsoft.Office.WebExtension.EventType.DialogEventReceived;e.clearEventHandlers(a);e.clearEventHandlers(i);var r=d[a],b=f(a),p=b[OSF.DDA.DispIdHost.Delegates.CloseDialog]!=undefined?b[OSF.DDA.DispIdHost.Delegates.CloseDialog]:b[OSF.DDA.DispIdHost.Delegates.UnregisterEventAsync];p({eventType:a,dispId:r,targetId:o,onCalling:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.IssueCall)},onReceiving:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.ReceiveResponse)},onComplete:n})}catch(m){j(m,k,h,l)}if(g!=OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess)throw OSF.OUtil.formatString(Strings.OfficeOM.L_FunctionCallFailed,OSF.DDA.AsyncMethodNames.CloseAsync.displayName,g)};g[OSF.DDA.DispIdHost.Methods.MessageParent]=function(a,i){var c={},b=OSF.DDA.SyncMethodCalls[OSF.DDA.SyncMethodNames.MessageParent.id],e=b.verifyAndExtractCall(a,i,c),g=f(OSF.DDA.SyncMethodNames.MessageParent.id),h=g[OSF.DDA.DispIdHost.Delegates.MessageParent],j=d[OSF.DDA.SyncMethodNames.MessageParent.id];return h({dispId:j,hostCallArgs:e,onCalling:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.IssueCall)},onReceiving:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.ReceiveResponse)}})};g[OSF.DDA.DispIdHost.Methods.SendMessage]=function(a,k,i){var c={},b=OSF.DDA.SyncMethodCalls[OSF.DDA.SyncMethodNames.SendMessage.id],e=b.verifyAndExtractCall(a,i,c),g=f(OSF.DDA.SyncMethodNames.SendMessage.id),h=g[OSF.DDA.DispIdHost.Delegates.SendMessage],j=d[OSF.DDA.SyncMethodNames.SendMessage.id];return h({dispId:j,hostCallArgs:e,onCalling:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.IssueCall)},onReceiving:function(){OSF.OUtil.writeProfilerMark(OSF.HostCallPerfMarker.ReceiveResponse)}})}};OSF.DDA.DispIdHost.addAsyncMethods=function(a,b,e){for(var f in b){var c=b[f],d=c.displayName;!a[d]&&OSF.OUtil.defineEnumerableProperty(a,d,{value:function(b){return function(){var c=OSF._OfficeAppFactory.getHostFacade()[OSF.DDA.DispIdHost.Methods.InvokeMethod];c(b,arguments,a,e)}}(c)})}};OSF.DDA.DispIdHost.addEventSupport=function(a,b,e){var d=OSF.DDA.AsyncMethodNames.AddHandlerAsync.displayName,c=OSF.DDA.AsyncMethodNames.RemoveHandlerAsync.displayName;!a[d]&&OSF.OUtil.defineEnumerableProperty(a,d,{value:function(){var c=OSF._OfficeAppFactory.getHostFacade()[OSF.DDA.DispIdHost.Methods.AddEventHandler];c(arguments,b,a,e)}});!a[c]&&OSF.OUtil.defineEnumerableProperty(a,c,{value:function(){var c=OSF._OfficeAppFactory.getHostFacade()[OSF.DDA.DispIdHost.Methods.RemoveEventHandler];c(arguments,b,a)}})};(function(g){var c="\n",d=true,a=null,b="undefined",j=function(){function c(){}c.isInstanceOfType=function(f,e){if(typeof e===b||e===a)return false;if(e instanceof f)return d;var c=e.constructor;if(!c||typeof c!=="function"||!c.__typeName||c.__typeName==="Object")c=Object;return !!(c===f)||c.__typeName&&f.__typeName&&c.__typeName===f.__typeName};return c}();g.MsAjaxTypeHelper=j;var h=function(){var e="Parameter name: {0}";function d(){}d.create=function(c,b){var a=new Error(c);a.message=c;if(b)for(var d in b)a[d]=b[d];a.popStackFrame();return a};d.parameterCount=function(a){var c="Sys.ParameterCountException: "+(a?a:"Parameter count mismatch."),b=d.create(c,{name:"Sys.ParameterCountException"});b.popStackFrame();return b};d.argument=function(a,g){var b="Sys.ArgumentException: "+(g?g:"Value does not fall within the expected range.");if(a)b+=c+f.format(e,a);var h=d.create(b,{name:"Sys.ArgumentException",paramName:a});h.popStackFrame();return h};d.argumentNull=function(a,g){var b="Sys.ArgumentNullException: "+(g?g:"Value cannot be null.");if(a)b+=c+f.format(e,a);var h=d.create(b,{name:"Sys.ArgumentNullException",paramName:a});h.popStackFrame();return h};d.argumentOutOfRange=function(i,g,j){var h="Sys.ArgumentOutOfRangeException: "+(j?j:"Specified argument was out of the range of valid values.");if(i)h+=c+f.format(e,i);if(typeof g!==b&&g!==a)h+=c+f.format("Actual value was {0}.",g);var k=d.create(h,{name:"Sys.ArgumentOutOfRangeException",paramName:i,actualValue:g});k.popStackFrame();return k};d.argumentType=function(h,g,b,i){var a="Sys.ArgumentTypeException: ";if(i)a+=i;else if(g&&b)a+=f.format("Object of type '{0}' cannot be converted to type '{1}'.",g.getName?g.getName():g,b.getName?b.getName():b);else a+="Object cannot be converted to the required type.";if(h)a+=c+f.format(e,h);var j=d.create(a,{name:"Sys.ArgumentTypeException",paramName:h,actualType:g,expectedType:b});j.popStackFrame();return j};d.argumentUndefined=function(a,g){var b="Sys.ArgumentUndefinedException: "+(g?g:"Value cannot be undefined.");if(a)b+=c+f.format(e,a);var h=d.create(b,{name:"Sys.ArgumentUndefinedException",paramName:a});h.popStackFrame();return h};d.invalidOperation=function(a){var c="Sys.InvalidOperationException: "+(a?a:"Operation is not valid due to the current state of the object."),b=d.create(c,{name:"Sys.InvalidOperationException"});b.popStackFrame();return b};return d}();g.MsAjaxError=h;var f=function(){function a(){}a.format=function(c){for(var b=[],a=1;a<arguments.length;a++)b[a-1]=arguments[a];var d=c;return d.replace(/{(\d+)}/gm,function(d,a){var c=parseInt(a,10);return b[c]===undefined?"{"+a+"}":b[c]})};a.startsWith=function(b,a){return b.substr(0,a.length)===a};return a}();g.MsAjaxString=f;var i=function(){function a(){}a.trace=function(){};return a}();g.MsAjaxDebug=i;if(!OsfMsAjaxFactory.isMsAjaxLoaded()){var e=function(b,d,c){if(b.__typeName===undefined||b.__typeName===a)b.__typeName=d;if(b.__class===undefined||b.__class===a)b.__class=c};e(Function,"Function",d);e(Error,"Error",d);e(Object,"Object",d);e(String,"String",d);e(Boolean,"Boolean",d);e(Date,"Date",d);e(Number,"Number",d);e(RegExp,"RegExp",d);e(Array,"Array",d);if(!Function.createCallback)Function.createCallback=function(b,a){var c=Function._validateParams(arguments,[{name:"method",type:Function},{name:"context",mayBeNull:d}]);if(c)throw c;return function(){var e=arguments.length;if(e>0){for(var d=[],c=0;c<e;c++)d[c]=arguments[c];d[e]=a;return b.apply(this,d)}return b.call(this,a)}};if(!Function.createDelegate)Function.createDelegate=function(b,c){var a=Function._validateParams(arguments,[{name:"instance",mayBeNull:d},{name:"method",type:Function}]);if(a)throw a;return function(){return c.apply(b,arguments)}};if(!Function._validateParams)Function._validateParams=function(i,g,e){var c,f=g.length;e=e||typeof e===b;c=Function._validateParameterCount(i,g,e);if(c){c.popStackFrame();return c}for(var d=0,k=i.length;d<k;d++){var h=g[Math.min(d,f-1)],j=h.name;if(h.parameterArray)j+="["+(d-f+1)+"]";else if(!e&&d>=f)break;c=Function._validateParameter(i[d],h,j);if(c){c.popStackFrame();return c}}return a};if(!Function._validateParameterCount)Function._validateParameterCount=function(m,f,l){var b,e,c=f.length,g=m.length;if(g<c){var i=c;for(b=0;b<c;b++){var j=f[b];if(j.optional||j.parameterArray)i--}if(g<i)e=d}else if(l&&g>c){e=d;for(b=0;b<c;b++)if(f[b].parameterArray){e=false;break}}if(e){var k=h.parameterCount();k.popStackFrame();return k}return a};if(!Function._validateParameter)Function._validateParameter=function(e,c,j){var d,i=c.type,n=!!c.integer,m=!!c.domElement,o=!!c.mayBeNull;d=Function._validateParameterType(e,i,n,m,o,j);if(d){d.popStackFrame();return d}var g=c.elementType,h=!!c.elementMayBeNull;if(i===Array&&typeof e!==b&&e!==a&&(g||!h))for(var l=!!c.elementInteger,k=!!c.elementDomElement,f=0;f<e.length;f++){var p=e[f];d=Function._validateParameterType(p,g,l,k,h,j+"["+f+"]");if(d){d.popStackFrame();return d}}return a};if(!Function._validateParameterType)Function._validateParameterType=function(d,e,j,i,h,f){var c,k;if(typeof d===b)if(h)return a;else{c=g.MsAjaxError.argumentUndefined(f);c.popStackFrame();return c}if(d===a)if(h)return a;else{c=g.MsAjaxError.argumentNull(f);c.popStackFrame();return c}if(e&&!g.MsAjaxTypeHelper.isInstanceOfType(e,d)){c=g.MsAjaxError.argumentType(f,typeof d,e);c.popStackFrame();return c}return a};if(!window.Type)window.Type=Function;if(!Type.registerNamespace)Type.registerNamespace=function(d){for(var c=d.split("."),b=window,a=0;a<c.length;a++){b[c[a]]=b[c[a]]||{};b=b[c[a]]}};if(!Type.prototype.registerClass)Type.prototype.registerClass=function(a){a={}};typeof Sys===b&&Type.registerNamespace("Sys");if(!Error.prototype.popStackFrame)Error.prototype.popStackFrame=function(){var d=this;if(arguments.length!==0)throw h.parameterCount();if(typeof d.stack===b||d.stack===a||typeof d.fileName===b||d.fileName===a||typeof d.lineNumber===b||d.lineNumber===a)return;var e=d.stack.split(c),g=e[0],j=d.fileName+":"+d.lineNumber;while(typeof g!==b&&g!==a&&g.indexOf(j)===-1){e.shift();g=e[0]}var i=e[1];if(typeof i===b||i===a)return;var f=i.match(/@(.*):(\d+)$/);if(typeof f===b||f===a)return;d.fileName=f[1];d.lineNumber=parseInt(f[2]);e.shift();d.stack=e.join(c)};OsfMsAjaxFactory.msAjaxError=h;OsfMsAjaxFactory.msAjaxString=f;OsfMsAjaxFactory.msAjaxDebug=i}})(OfficeExt||(OfficeExt={}));OSF.OUtil.setNamespace("SafeArray",OSF.DDA);OSF.DDA.SafeArray.Response={Status:0,Payload:1};OSF.DDA.SafeArray.UniqueArguments={Offset:"offset",Run:"run",BindingSpecificData:"bindingSpecificData",MergedCellGuid:"{66e7831f-81b2-42e2-823c-89e872d541b3}"};OSF.OUtil.setNamespace("Delegate",OSF.DDA.SafeArray);OSF.DDA.SafeArray.Delegate._onException=function(d,b){var a,c=d.number;if(c)switch(c){case -2146828218:a=OSF.DDA.ErrorCodeManager.errorCodes.ooeNoCapability;break;case -2147467259:if(b.dispId==OSF.DDA.EventDispId.dispidDialogMessageReceivedEvent)a=OSF.DDA.ErrorCodeManager.errorCodes.ooeDialogAlreadyOpened;else a=OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError;break;case -2146828283:a=OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidParam;break;case -2147209089:a=OSF.DDA.ErrorCodeManager.errorCodes.ooeInvalidParam;break;case -2147208704:a=OSF.DDA.ErrorCodeManager.errorCodes.ooeTooManyIncompleteRequests;break;case -2146827850:default:a=OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError}b.onComplete&&b.onComplete(a||OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError)};OSF.DDA.SafeArray.Delegate._onExceptionSyncMethod=function(c){var a,b=c.number;if(b)switch(b){case -2146828218:a=OSF.DDA.ErrorCodeManager.errorCodes.ooeNoCapability;break;case -2146827850:default:a=OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError}return a||OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError};OSF.DDA.SafeArray.Delegate.SpecialProcessor=function(){function a(a){var b;try{var h=a.ubound(1),d=a.ubound(2);a=a.toArray();if(h==1&&d==1)b=[a];else{b=[];for(var f=0;f<h;f++){for(var c=[],e=0;e<d;e++){var g=a[f*d+e];g!=OSF.DDA.SafeArray.UniqueArguments.MergedCellGuid&&c.push(g)}c.length>0&&b.push(c)}}}catch(i){}return b}var c=[],b={};b[Microsoft.Office.WebExtension.Parameters.Data]=function(){var c=0,b=1;return {toHost:function(a){if(OSF.DDA.TableDataProperties&&typeof a!="string"&&a[OSF.DDA.TableDataProperties.TableRows]!==undefined){var d=[];d[c]=a[OSF.DDA.TableDataProperties.TableRows];d[b]=a[OSF.DDA.TableDataProperties.TableHeaders];a=d}return a},fromHost:function(f){var e;if(f.toArray){var g=f.dimensions();if(g===2)e=a(f);else{var d=f.toArray();if(d.length===2&&(d[0]!=null&&d[0].toArray||d[1]!=null&&d[1].toArray)){e={};e[OSF.DDA.TableDataProperties.TableRows]=a(d[c]);e[OSF.DDA.TableDataProperties.TableHeaders]=a(d[b])}else e=d}}else e=f;return e}}}();OSF.DDA.SafeArray.Delegate.SpecialProcessor.uber.constructor.call(this,c,b);this.unpack=function(c,a){var d;if(this.isComplexType(c)||OSF.DDA.ListType.isListType(c)){var e=a!==undefined&&a.toArray!==undefined;d=e?a.toArray():a||{}}else if(this.isDynamicType(c))d=b[c].fromHost(a);else d=a;return d}};OSF.OUtil.extend(OSF.DDA.SafeArray.Delegate.SpecialProcessor,OSF.DDA.SpecialProcessor);OSF.DDA.SafeArray.Delegate.ParameterMap=OSF.DDA.getDecoratedParameterMap(new OSF.DDA.SafeArray.Delegate.SpecialProcessor,[{type:Microsoft.Office.WebExtension.Parameters.ValueFormat,toHost:[{name:Microsoft.Office.WebExtension.ValueFormat.Unformatted,value:0},{name:Microsoft.Office.WebExtension.ValueFormat.Formatted,value:1}]},{type:Microsoft.Office.WebExtension.Parameters.FilterType,toHost:[{name:Microsoft.Office.WebExtension.FilterType.All,value:0}]}]);OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.PropertyDescriptors.AsyncResultStatus,fromHost:[{name:Microsoft.Office.WebExtension.AsyncResultStatus.Succeeded,value:0},{name:Microsoft.Office.WebExtension.AsyncResultStatus.Failed,value:1}]});OSF.DDA.SafeArray.Delegate.executeAsync=function(a){function c(a){var b=a;if(OSF.OUtil.isArray(a))for(var f=b.length,d=0;d<f;d++)b[d]=c(b[d]);else if(OSF.OUtil.isDate(a))b=a.getVarDate();else if(typeof a==="object"&&!OSF.OUtil.isArray(a)){b=[];for(var e in a)if(!OSF.OUtil.isFunction(a[e]))b[e]=c(a[e])}return b}function b(a){var e=a;if(a!=null&&a.toArray){var d=a.toArray();e=new Array(d.length);for(var c=0;c<d.length;c++)e[c]=b(d[c])}return e}try{a.onCalling&&a.onCalling();OSF.ClientHostController.execute(a.dispId,c(a.hostCallArgs),function(g){var d,e;if(typeof g==="number"){d=[];e=g}else{d=g.toArray();e=d[OSF.DDA.SafeArray.Response.Status]}if(e==OSF.DDA.ErrorCodeManager.errorCodes.ooeChunkResult){var c=d[OSF.DDA.SafeArray.Response.Payload];c=b(c);if(c!=null){if(!a._chunkResultData)a._chunkResultData=[];a._chunkResultData[c[0]]=c[1]}return false}a.onReceiving&&a.onReceiving();if(a.onComplete){var c;if(e==OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess){if(d.length>2){c=[];for(var f=1;f<d.length;f++)c[f-1]=d[f]}else c=d[OSF.DDA.SafeArray.Response.Payload];if(a._chunkResultData){c=b(c);if(c!=null){var h=c[c.length-1];if(a._chunkResultData.length==h)c[c.length-1]=a._chunkResultData;else e=OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError}}}else c=d[OSF.DDA.SafeArray.Response.Payload];a.onComplete(e,c)}return true})}catch(d){OSF.DDA.SafeArray.Delegate._onException(d,a)}};OSF.DDA.SafeArray.Delegate._getOnAfterRegisterEvent=function(c,a){var b=(new Date).getTime();return function(d){a.onReceiving&&a.onReceiving();var e=d.toArray?d.toArray()[OSF.DDA.SafeArray.Response.Status]:d;a.onComplete&&a.onComplete(e);OSF.AppTelemetry&&OSF.AppTelemetry.onRegisterDone(c,a.dispId,Math.abs((new Date).getTime()-b),e)}};OSF.DDA.SafeArray.Delegate.registerEventAsync=function(a){a.onCalling&&a.onCalling();var c=OSF.DDA.SafeArray.Delegate._getOnAfterRegisterEvent(true,a);try{OSF.ClientHostController.registerEvent(a.dispId,a.targetId,function(c,b){a.onEvent&&a.onEvent(b);OSF.AppTelemetry&&OSF.AppTelemetry.onEventDone(a.dispId)},c)}catch(b){OSF.DDA.SafeArray.Delegate._onException(b,a)}};OSF.DDA.SafeArray.Delegate.unregisterEventAsync=function(a){a.onCalling&&a.onCalling();var c=OSF.DDA.SafeArray.Delegate._getOnAfterRegisterEvent(false,a);try{OSF.ClientHostController.unregisterEvent(a.dispId,a.targetId,c)}catch(b){OSF.DDA.SafeArray.Delegate._onException(b,a)}};OSF.ClientMode={ReadWrite:0,ReadOnly:1};OSF.DDA.RichInitializationReason={1:Microsoft.Office.WebExtension.InitializationReason.Inserted,2:Microsoft.Office.WebExtension.InitializationReason.DocumentOpened};OSF.InitializationHelper=function(d,b,f,e,c){var a=this;a._hostInfo=d;a._webAppState=b;a._context=f;a._settings=e;a._hostFacade=c;a._initializeSettings=a.initializeSettings};OSF.InitializationHelper.prototype.deserializeSettings=function(b,f){var d,c=OSF.OUtil.getSessionStorage();if(c){var a=c.getItem(OSF._OfficeAppFactory.getCachedSessionSettingsKey());if(a)b=JSON.parse(a);else{a=JSON.stringify(b);c.setItem(OSF._OfficeAppFactory.getCachedSessionSettingsKey(),a)}}var e=OSF.DDA.SettingsManager.deserializeSettings(b);if(f)d=new OSF.DDA.RefreshableSettings(e);else d=new OSF.DDA.Settings(e);return d};OSF.InitializationHelper.prototype.saveAndSetDialogInfo=function(){};OSF.InitializationHelper.prototype.setAgaveHostCommunication=function(){};OSF.InitializationHelper.prototype.prepareRightBeforeWebExtensionInitialize=function(a){this.prepareApiSurface(a);Microsoft.Office.WebExtension.initialize(this.getInitializationReason(a))};OSF.InitializationHelper.prototype.prepareApiSurface=function(a){var e=new OSF.DDA.License(a.get_eToken()),d=OSF.DDA.OfficeTheme&&OSF.DDA.OfficeTheme.getOfficeTheme?OSF.DDA.OfficeTheme.getOfficeTheme:null;if(a.get_isDialog()){if(OSF.DDA.UI.ChildUI)a.ui=new OSF.DDA.UI.ChildUI}else if(OSF.DDA.UI.ParentUI){a.ui=new OSF.DDA.UI.ParentUI;OfficeExt.Container&&OSF.DDA.DispIdHost.addAsyncMethods(a.ui,[OSF.DDA.AsyncMethodNames.CloseContainerAsync])}OSF.DDA.OpenBrowser&&OSF.DDA.DispIdHost.addAsyncMethods(a.ui,[OSF.DDA.AsyncMethodNames.OpenBrowserWindow]);OSF.DDA.ExecuteFeature&&OSF.DDA.DispIdHost.addAsyncMethods(a.ui,[OSF.DDA.AsyncMethodNames.ExecuteFeature]);OSF.DDA.QueryFeature&&OSF.DDA.DispIdHost.addAsyncMethods(a.ui,[OSF.DDA.AsyncMethodNames.QueryFeature]);if(OSF.DDA.Auth){a.auth=new OSF.DDA.Auth;OSF.DDA.DispIdHost.addAsyncMethods(a.auth,[OSF.DDA.AsyncMethodNames.GetAccessTokenAsync])}OSF._OfficeAppFactory.setContext(new OSF.DDA.Context(a,a.doc,e,null,d));var b,c;b=OSF.DDA.DispIdHost.getClientDelegateMethods;c=OSF.DDA.SafeArray.Delegate.ParameterMap;OSF._OfficeAppFactory.setHostFacade(new OSF.DDA.DispIdHost.Facade(b,c))};OSF.InitializationHelper.prototype.getInitializationReason=function(a){return OSF.DDA.RichInitializationReason[a.get_reason()]};OSF.DDA.DispIdHost.getClientDelegateMethods=function(b){var a={};a[OSF.DDA.DispIdHost.Delegates.ExecuteAsync]=OSF.DDA.SafeArray.Delegate.executeAsync;a[OSF.DDA.DispIdHost.Delegates.RegisterEventAsync]=OSF.DDA.SafeArray.Delegate.registerEventAsync;a[OSF.DDA.DispIdHost.Delegates.UnregisterEventAsync]=OSF.DDA.SafeArray.Delegate.unregisterEventAsync;a[OSF.DDA.DispIdHost.Delegates.OpenDialog]=OSF.DDA.SafeArray.Delegate.openDialog;a[OSF.DDA.DispIdHost.Delegates.CloseDialog]=OSF.DDA.SafeArray.Delegate.closeDialog;a[OSF.DDA.DispIdHost.Delegates.MessageParent]=OSF.DDA.SafeArray.Delegate.messageParent;a[OSF.DDA.DispIdHost.Delegates.SendMessage]=OSF.DDA.SafeArray.Delegate.sendMessage;if(OSF.DDA.AsyncMethodNames.RefreshAsync&&b==OSF.DDA.AsyncMethodNames.RefreshAsync.id){var d=function(c,b,a){if(typeof OSF.DDA.ClientSettingsManager.refresh==="function")return OSF.DDA.ClientSettingsManager.refresh(b,a);else return OSF.DDA.ClientSettingsManager.read(b,a)};a[OSF.DDA.DispIdHost.Delegates.ExecuteAsync]=OSF.DDA.ClientSettingsManager.getSettingsExecuteMethod(d)}if(OSF.DDA.AsyncMethodNames.SaveAsync&&b==OSF.DDA.AsyncMethodNames.SaveAsync.id){var c=function(a,c,b){return OSF.DDA.ClientSettingsManager.write(a[OSF.DDA.SettingsManager.SerializedSettings],a[Microsoft.Office.WebExtension.Parameters.OverwriteIfStale],c,b)};a[OSF.DDA.DispIdHost.Delegates.ExecuteAsync]=OSF.DDA.ClientSettingsManager.getSettingsExecuteMethod(c)}return a};(function(b){var a=function(){var a="undefined";function b(){}b.prototype.execute=function(d,c,b){if(typeof OsfOMToken!=a&&OsfOMToken)window.external.Execute(d,c,b,OsfOMToken);else window.external.Execute(d,c,b)};b.prototype.registerEvent=function(e,c,d,b){if(typeof OsfOMToken!=a&&OsfOMToken)window.external.RegisterEvent(e,c,d,b,OsfOMToken);else window.external.RegisterEvent(e,c,d,b)};b.prototype.unregisterEvent=function(d,c,b){if(typeof OsfOMToken!=a&&OsfOMToken)window.external.UnregisterEvent(d,c,b,OsfOMToken);else window.external.UnregisterEvent(d,c,b)};return b}();b.RichClientHostController=a})(OfficeExt||(OfficeExt={}));(function(a){var b=function(c){var b="undefined";__extends(a,c);function a(){return c!==null&&c.apply(this,arguments)||this}a.prototype.messageParent=function(a){if(OSF.OUtil.externalNativeFunctionExists(typeof window.external.MessageParent2)){if(a){var c=a[Microsoft.Office.WebExtension.Parameters.MessageToParent];if(typeof c==="boolean")if(c===true)a[Microsoft.Office.WebExtension.Parameters.MessageToParent]="true";else if(c===false)a[Microsoft.Office.WebExtension.Parameters.MessageToParent]=""}if(typeof OsfOMToken!=b&&OsfOMToken)window.external.MessageParent2(JSON.stringify(a),OsfOMToken);else window.external.MessageParent2(JSON.stringify(a))}else{var d=a[Microsoft.Office.WebExtension.Parameters.MessageToParent];window.external.MessageParent(d)}};a.prototype.openDialog=function(d,b,c,a){this.registerEvent(d,b,c,a)};a.prototype.closeDialog=function(c,b,a){this.unregisterEvent(c,b,a)};a.prototype.sendMessage=function(a){if(OSF.OUtil.externalNativeFunctionExists(typeof window.external.MessageChild2))if(typeof OsfOMToken!=b&&OsfOMToken)window.external.MessageChild2(JSON.stringify(a),OsfOMToken);else window.external.MessageChild2(JSON.stringify(a));else{var c=a[Microsoft.Office.WebExtension.Parameters.MessageContent];window.external.MessageChild(c)}};return a}(a.RichClientHostController);a.Win32RichClientHostController=b})(OfficeExt||(OfficeExt={}));(function(a){var b;(function(c){var b=function(){var a=null;function b(){this._osfOfficeTheme=a;this._osfOfficeThemeTimeStamp=a}b.prototype.getOfficeTheme=function(){var c="GetOfficeThemeInfo",a=this;if(OSF.DDA._OsfControlContext){if(a._osfOfficeTheme&&a._osfOfficeThemeTimeStamp&&(new Date).getTime()-a._osfOfficeThemeTimeStamp<b._osfOfficeThemeCacheValidPeriod)OSF.AppTelemetry&&OSF.AppTelemetry.onPropertyDone(c,0);else{var g=(new Date).getTime(),f=OSF.DDA._OsfControlContext.GetOfficeThemeInfo(),d=(new Date).getTime();OSF.AppTelemetry&&OSF.AppTelemetry.onPropertyDone(c,Math.abs(d-g));a._osfOfficeTheme=JSON.parse(f);for(var e in a._osfOfficeTheme)a._osfOfficeTheme[e]=OSF.OUtil.convertIntToCssHexColor(a._osfOfficeTheme[e]);a._osfOfficeThemeTimeStamp=d}return a._osfOfficeTheme}};b.instance=function(){if(b._instance==a)b._instance=new b;return b._instance};b._osfOfficeThemeCacheValidPeriod=5e3;b._instance=a;return b}();c.OfficeThemeManager=b;OSF.OUtil.setNamespace("OfficeTheme",OSF.DDA);OSF.DDA.OfficeTheme.getOfficeTheme=a.OfficeTheme.OfficeThemeManager.instance().getOfficeTheme})(b=a.OfficeTheme||(a.OfficeTheme={}))})(OfficeExt||(OfficeExt={}));OSF.initializeRichCommon=function(){var a="undefined";OSF.DDA.ClientSettingsManager={getSettingsExecuteMethod:function(a){return function(b){var e=function(c,a){b.onReceiving&&b.onReceiving();b.onComplete&&b.onComplete(c,a)},c;try{c=a(b.hostCallArgs,b.onCalling,e)}catch(d){var f=OSF.DDA.ErrorCodeManager.errorCodes.ooeInternalError;c={name:Strings.OfficeOM.L_InternalError,message:d};b.onComplete&&b.onComplete(f,c)}}},read:function(g,f){var c=[],e=[];g&&g();if(typeof OsfOMToken!=a&&OsfOMToken)OSF.DDA._OsfControlContext.GetSettings(OsfOMToken).Read(c,e);else OSF.DDA._OsfControlContext.GetSettings().Read(c,e);for(var d={},b=0;b<c.length;b++)d[c[b]]=e[b];f&&f(OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess,d);return d},write:function(f,i,g,c){var e=[],d=[];for(var h in f){e.push(h);d.push(f[h])}g&&g();var b;if(typeof OsfOMToken!=a&&OsfOMToken)b=OSF.DDA._OsfControlContext.GetSettings(OsfOMToken);else b=OSF.DDA._OsfControlContext.GetSettings();if(typeof b.WriteAsync!=a)b.WriteAsync(e,d,c);else{b.Write(e,d);c&&c(OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess)}},refresh:function(f,e){var c=[],g=[];f&&f();var b;if(typeof OsfOMToken!=a&&OsfOMToken)b=OSF.DDA._OsfControlContext.GetSettings(OsfOMToken);else b=OSF.DDA._OsfControlContext.GetSettings();var d=function(){b.Read(c,g);for(var d={},a=0;a<c.length;a++)d[c[a]]=g[a];e&&e(OSF.DDA.ErrorCodeManager.errorCodes.ooeSuccess,d)};if(b.RefreshAsync)b.RefreshAsync(function(){d()});else d()}};OSF.InitializationHelper.prototype.initializeSettings=function(b){var a=OSF.DDA.ClientSettingsManager.read(),c=this.deserializeSettings(a,b);return c};OSF.InitializationHelper.prototype.getAppContext=function(D,r){var c,a,p="Warning: Office.js is loaded outside of Office client";try{if(window.external&&OSF.OUtil.externalNativeFunctionExists(typeof window.external.GetContext))a=OSF.DDA._OsfControlContext=window.external.GetContext();else{OsfMsAjaxFactory.msAjaxDebug.trace(p);return}}catch(C){OsfMsAjaxFactory.msAjaxDebug.trace(p);return}var x=a.GetAppType(),B=a.GetSolutionRef(),y=a.GetAppVersionMajor(),s=a.GetAppVersionMinor(),w=a.GetAppUILocale(),u=a.GetAppDataLocale(),z=a.GetDocUrl(),t=a.GetAppCapabilities(),A=a.GetActivationMode(),q=a.GetControlIntegrationLevel(),v=[],b;try{b=a.GetSolutionToken()}catch(d){}var n;if(OSF.OUtil.externalNativeFunctionExists(typeof a.GetCorrelationId))n=a.GetCorrelationId();var m;if(OSF.OUtil.externalNativeFunctionExists(typeof a.GetInstanceId))m=a.GetInstanceId();var o;if(OSF.OUtil.externalNativeFunctionExists(typeof a.GetTouchEnabled))o=a.GetTouchEnabled();var j;if(OSF.OUtil.externalNativeFunctionExists(typeof a.GetCommerceAllowed))j=a.GetCommerceAllowed();var i;if(OSF.OUtil.externalNativeFunctionExists(typeof a.GetSupportedMatrix))i=a.GetSupportedMatrix();var h;if(OSF.OUtil.externalNativeFunctionExists(typeof a.GetHostCustomMessage))h=a.GetHostCustomMessage();var k;if(OSF.OUtil.externalNativeFunctionExists(typeof a.GetHostFullVersion))k=a.GetHostFullVersion();var e;if(OSF.OUtil.externalNativeFunctionExists(typeof a.GetDialogRequirementMatrix))e=a.GetDialogRequirementMatrix();var l;if(OSF.OUtil.externalNativeFunctionExists(typeof a.GetFeaturesForSolution))try{var f=a.GetFeaturesForSolution();if(f)l=JSON.parse(f)}catch(d){OsfMsAjaxFactory.msAjaxDebug.trace("Exception while creating the SDX FeatureGates object. Details: "+d)}var g=0;if(OSF.OUtil.externalNativeFunctionExists(typeof a.GetInitialDisplayMode))g=a.GetInitialDisplayMode();b=b?b.toString():"";c=new OSF.OfficeAppContext(B,x,y,w,u,z,t,v,A,q,b,n,m,o,j,s,i,h,k,undefined,undefined,undefined,undefined,e,l,undefined,g);OSF.AppTelemetry&&OSF.AppTelemetry.initialize(c);r(c)}};OSF.ClientHostController=new OfficeExt.Win32RichClientHostController;OSF.initializeRichCommon();var OSFLog;(function(g){var e="ResponseTime",d="Message",c="SessionId",b="CorrelationId",a=true,f=function(){function b(a){this._table=a;this._fields={}}Object.defineProperty(b.prototype,"Fields",{"get":function(){return this._fields},enumerable:a,configurable:a});Object.defineProperty(b.prototype,"Table",{"get":function(){return this._table},enumerable:a,configurable:a});b.prototype.SerializeFields=function(){};b.prototype.SetSerializedField=function(b,a){if(typeof a!=="undefined"&&a!==null)this._serializedFields[b]=a.toString()};b.prototype.SerializeRow=function(){var a=this;a._serializedFields={};a.SetSerializedField("Table",a._table);a.SerializeFields();return JSON.stringify(a._serializedFields)};return b}();g.BaseUsageData=f;var i=function(v){var u="IsFromWacAutomation",t="WacHostEnvironment",s="HostJSVersion",r="OfficeJSVersion",q="DocUrl",p="AppSizeHeight",o="AppSizeWidth",n="ClientId",m="HostVersion",l="Host",k="UserId",j="Browser",i="AssetId",h="AppURL",g="AppInstanceId",f="AppId";__extends(e,v);function e(){return v.call(this,"AppActivated")||this}Object.defineProperty(e.prototype,b,{"get":function(){return this.Fields[b]},"set":function(a){this.Fields[b]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,c,{"get":function(){return this.Fields[c]},"set":function(a){this.Fields[c]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,f,{"get":function(){return this.Fields[f]},"set":function(a){this.Fields[f]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,g,{"get":function(){return this.Fields[g]},"set":function(a){this.Fields[g]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,h,{"get":function(){return this.Fields[h]},"set":function(a){this.Fields[h]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,i,{"get":function(){return this.Fields[i]},"set":function(a){this.Fields[i]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,j,{"get":function(){return this.Fields[j]},"set":function(a){this.Fields[j]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,k,{"get":function(){return this.Fields[k]},"set":function(a){this.Fields[k]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,l,{"get":function(){return this.Fields[l]},"set":function(a){this.Fields[l]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,m,{"get":function(){return this.Fields[m]},"set":function(a){this.Fields[m]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,n,{"get":function(){return this.Fields[n]},"set":function(a){this.Fields[n]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,o,{"get":function(){return this.Fields[o]},"set":function(a){this.Fields[o]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,p,{"get":function(){return this.Fields[p]},"set":function(a){this.Fields[p]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,d,{"get":function(){return this.Fields[d]},"set":function(a){this.Fields[d]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,q,{"get":function(){return this.Fields[q]},"set":function(a){this.Fields[q]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,r,{"get":function(){return this.Fields[r]},"set":function(a){this.Fields[r]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,s,{"get":function(){return this.Fields[s]},"set":function(a){this.Fields[s]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,t,{"get":function(){return this.Fields[t]},"set":function(a){this.Fields[t]=a},enumerable:a,configurable:a});Object.defineProperty(e.prototype,u,{"get":function(){return this.Fields[u]},"set":function(a){this.Fields[u]=a},enumerable:a,configurable:a});e.prototype.SerializeFields=function(){var a=this;a.SetSerializedField(b,a.CorrelationId);a.SetSerializedField(c,a.SessionId);a.SetSerializedField(f,a.AppId);a.SetSerializedField(g,a.AppInstanceId);a.SetSerializedField(h,a.AppURL);a.SetSerializedField(i,a.AssetId);a.SetSerializedField(j,a.Browser);a.SetSerializedField(k,a.UserId);a.SetSerializedField(l,a.Host);a.SetSerializedField(m,a.HostVersion);a.SetSerializedField(n,a.ClientId);a.SetSerializedField(o,a.AppSizeWidth);a.SetSerializedField(p,a.AppSizeHeight);a.SetSerializedField(d,a.Message);a.SetSerializedField(q,a.DocUrl);a.SetSerializedField(r,a.OfficeJSVersion);a.SetSerializedField(s,a.HostJSVersion);a.SetSerializedField(t,a.WacHostEnvironment);a.SetSerializedField(u,a.IsFromWacAutomation)};return e}(f);g.AppActivatedUsageData=i;var k=function(h){var f="StartTime",d="ScriptId";__extends(g,h);function g(){return h.call(this,"ScriptLoad")||this}Object.defineProperty(g.prototype,b,{"get":function(){return this.Fields[b]},"set":function(a){this.Fields[b]=a},enumerable:a,configurable:a});Object.defineProperty(g.prototype,c,{"get":function(){return this.Fields[c]},"set":function(a){this.Fields[c]=a},enumerable:a,configurable:a});Object.defineProperty(g.prototype,d,{"get":function(){return this.Fields[d]},"set":function(a){this.Fields[d]=a},enumerable:a,configurable:a});Object.defineProperty(g.prototype,f,{"get":function(){return this.Fields[f]},"set":function(a){this.Fields[f]=a},enumerable:a,configurable:a});Object.defineProperty(g.prototype,e,{"get":function(){return this.Fields[e]},"set":function(a){this.Fields[e]=a},enumerable:a,configurable:a});g.prototype.SerializeFields=function(){var a=this;a.SetSerializedField(b,a.CorrelationId);a.SetSerializedField(c,a.SessionId);a.SetSerializedField(d,a.ScriptId);a.SetSerializedField(f,a.StartTime);a.SetSerializedField(e,a.ResponseTime)};return g}(f);g.ScriptLoadUsageData=k;var l=function(j){var h="CloseMethod",g="OpenTime",f="AppSizeFinalHeight",e="AppSizeFinalWidth",d="FocusTime";__extends(i,j);function i(){return j.call(this,"AppClosed")||this}Object.defineProperty(i.prototype,b,{"get":function(){return this.Fields[b]},"set":function(a){this.Fields[b]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,c,{"get":function(){return this.Fields[c]},"set":function(a){this.Fields[c]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,d,{"get":function(){return this.Fields[d]},"set":function(a){this.Fields[d]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,e,{"get":function(){return this.Fields[e]},"set":function(a){this.Fields[e]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,f,{"get":function(){return this.Fields[f]},"set":function(a){this.Fields[f]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,g,{"get":function(){return this.Fields[g]},"set":function(a){this.Fields[g]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,h,{"get":function(){return this.Fields[h]},"set":function(a){this.Fields[h]=a},enumerable:a,configurable:a});i.prototype.SerializeFields=function(){var a=this;a.SetSerializedField(b,a.CorrelationId);a.SetSerializedField(c,a.SessionId);a.SetSerializedField(d,a.FocusTime);a.SetSerializedField(e,a.AppSizeFinalWidth);a.SetSerializedField(f,a.AppSizeFinalHeight);a.SetSerializedField(g,a.OpenTime);a.SetSerializedField(h,a.CloseMethod)};return i}(f);g.AppClosedUsageData=l;var m=function(j){var h="ErrorType",g="Parameters",f="APIID",d="APIType";__extends(i,j);function i(){return j.call(this,"APIUsage")||this}Object.defineProperty(i.prototype,b,{"get":function(){return this.Fields[b]},"set":function(a){this.Fields[b]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,c,{"get":function(){return this.Fields[c]},"set":function(a){this.Fields[c]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,d,{"get":function(){return this.Fields[d]},"set":function(a){this.Fields[d]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,f,{"get":function(){return this.Fields[f]},"set":function(a){this.Fields[f]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,g,{"get":function(){return this.Fields[g]},"set":function(a){this.Fields[g]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,e,{"get":function(){return this.Fields[e]},"set":function(a){this.Fields[e]=a},enumerable:a,configurable:a});Object.defineProperty(i.prototype,h,{"get":function(){return this.Fields[h]},"set":function(a){this.Fields[h]=a},enumerable:a,configurable:a});i.prototype.SerializeFields=function(){var a=this;a.SetSerializedField(b,a.CorrelationId);a.SetSerializedField(c,a.SessionId);a.SetSerializedField(d,a.APIType);a.SetSerializedField(f,a.APIID);a.SetSerializedField(g,a.Parameters);a.SetSerializedField(e,a.ResponseTime);a.SetSerializedField(h,a.ErrorType)};return i}(f);g.APIUsageUsageData=m;var h=function(g){var e="SuccessCode";__extends(f,g);function f(){return g.call(this,"AppInitialization")||this}Object.defineProperty(f.prototype,b,{"get":function(){return this.Fields[b]},"set":function(a){this.Fields[b]=a},enumerable:a,configurable:a});Object.defineProperty(f.prototype,c,{"get":function(){return this.Fields[c]},"set":function(a){this.Fields[c]=a},enumerable:a,configurable:a});Object.defineProperty(f.prototype,e,{"get":function(){return this.Fields[e]},"set":function(a){this.Fields[e]=a},enumerable:a,configurable:a});Object.defineProperty(f.prototype,d,{"get":function(){return this.Fields[d]},"set":function(a){this.Fields[d]=a},enumerable:a,configurable:a});f.prototype.SerializeFields=function(){var a=this;a.SetSerializedField(b,a.CorrelationId);a.SetSerializedField(c,a.SessionId);a.SetSerializedField(e,a.SuccessCode);a.SetSerializedField(d,a.Message)};return f}(f);g.AppInitializationUsageData=h;var j=function(h){var f="wacDomain",e="hostPlatform",d="hostType",c="instanceId",b="isWacKnownHost";__extends(g,h);function g(){return h.call(this,"CheckWACHost")||this}Object.defineProperty(g.prototype,b,{"get":function(){return this.Fields[b]},"set":function(a){this.Fields[b]=a},enumerable:a,configurable:a});Object.defineProperty(g.prototype,c,{"get":function(){return this.Fields[c]},"set":function(a){this.Fields[c]=a},enumerable:a,configurable:a});Object.defineProperty(g.prototype,d,{"get":function(){return this.Fields[d]},"set":function(a){this.Fields[d]=a},enumerable:a,configurable:a});Object.defineProperty(g.prototype,e,{"get":function(){return this.Fields[e]},"set":function(a){this.Fields[e]=a},enumerable:a,configurable:a});Object.defineProperty(g.prototype,f,{"get":function(){return this.Fields[f]},"set":function(a){this.Fields[f]=a},enumerable:a,configurable:a});g.prototype.SerializeFields=function(){var a=this;a.SetSerializedField(b,a.isWacKnownHost);a.SetSerializedField(c,a.instanceId);a.SetSerializedField(d,a.hostType);a.SetSerializedField(e,a.hostPlatform);a.SetSerializedField(f,a.wacDomain)};return g}(f);g.CheckWACHostUsageData=j})(OSFLog||(OSFLog={}));var Logger;(function(a){"use strict";var e;(function(a){a[a["info"]=0]="info";a[a["warning"]=1]="warning";a[a["error"]=2]="error"})(e=a.TraceLevel||(a.TraceLevel={}));var f;(function(a){a[a["none"]=0]="none";a[a["flush"]=1]="flush"})(f=a.SendFlag||(a.SendFlag={}));function b(){}a.allowUploadingData=b;function g(){}a.sendLog=g;function c(){try{return new d}catch(a){return null}}var d=function(){function a(){}a.prototype.writeLog=function(){};a.prototype.loadProxyFrame=function(){};return a}();if(!OSF.Logger)OSF.Logger=a;a.ulsEndpoint=c()})(Logger||(Logger={}));var OSFAriaLogger;(function(w){var e="undefined",f="hostPlatform",h="hostType",j="ResponseTime",g="double",a=false,d="int64",b="string",c=true,l={name:"AppActivated",enabled:c,critical:c,points:[{name:"Browser",type:b},{name:"Message",type:b},{name:"Host",type:b},{name:"AppSizeWidth",type:d},{name:"AppSizeHeight",type:d},{name:"IsFromWacAutomation",type:b}]},n={name:"ScriptLoad",enabled:c,critical:a,points:[{name:"ScriptId",type:b},{name:"StartTime",type:g},{name:j,type:g}]},v=o(),s={name:"APIUsage",enabled:v,critical:a,points:[{name:"APIType",type:b},{name:"APIID",type:d},{name:"Parameters",type:b},{name:j,type:d},{name:"ErrorType",type:d}]},k={name:"AppInitialization",enabled:c,critical:a,points:[{name:"SuccessCode",type:d},{name:"Message",type:b}]},p={name:"AppClosed",enabled:c,critical:a,points:[{name:"FocusTime",type:d},{name:"AppSizeFinalWidth",type:d},{name:"AppSizeFinalHeight",type:d},{name:"OpenTime",type:d}]},m={name:"CheckWACHost",enabled:c,critical:a,points:[{name:"isWacKnownHost",type:d},{name:"solutionId",type:b},{name:h,type:b},{name:f,type:b},{name:"correlationId",type:b}]},u=[l,n,s,k,p,m];function t(a,e){var f=e.rename===undefined?e.name:e.rename,h=e.type,c=undefined;switch(h){case b:c=oteljs.makeStringDataField(f,a);break;case g:if(typeof a===b)a=parseFloat(a);c=oteljs.makeDoubleDataField(f,a);break;case d:if(typeof a===b)a=parseInt(a);c=oteljs.makeInt64DataField(f,a);break;case "boolean":if(typeof a===b)a=a==="true";c=oteljs.makeBooleanDataField(f,a)}return c}function i(d){for(var a=0,b=u;a<b.length;a++){var c=b[a];if(c.name===d)return c}return undefined}function x(c){var b=i(c);if(b===undefined)return a;return b.enabled}function o(){if(!OSF._OfficeAppFactory||!OSF._OfficeAppFactory.getHostInfo)return a;var b=OSF._OfficeAppFactory.getHostInfo();if(!b)return a;switch(b[h]){case "outlook":switch(b[f]){case "mac":case "web":return c;default:return a}default:return a}}function q(e,l){var a=i(e);if(a===undefined)return undefined;for(var d=[],c=0,j=a.points;c<j.length;c++){var g=j[c],n=g.name,h=l[n];if(h===undefined)continue;var f=t(h,g);f!==undefined&&d.push(f)}var b={dataCategories:oteljs.DataCategories.ProductServiceUsage};if(a.critical)b.samplingPolicy=oteljs.SamplingPolicy.CriticalBusinessImpact;b.diagnosticLevel=oteljs.DiagnosticLevel.NecessaryServiceDataEvent;var k="Office.Extensibility.OfficeJs."+e+"X",m={eventName:k,dataFields:d,eventFlags:b};return m}function r(a,b){if(x(a))typeof OTel!==e&&OTel.OTelLogger.onTelemetryLoaded(function(){var c=q(a,b);if(c===undefined)return;Microsoft.Office.WebExtension.sendTelemetryEvent(c)})}var y=function(){function b(){}b.prototype.getAriaCDNLocation=function(){return OSF._OfficeAppFactory.getLoadScriptHelper().getOfficeJsBasePath()+"ariatelemetry/aria-web-telemetry.js"};b.getInstance=function(){if(b.AriaLoggerObj===undefined)b.AriaLoggerObj=new b;return b.AriaLoggerObj};b.prototype.isIUsageData=function(a){return a["Fields"]!==undefined};b.prototype.shouldSendDirectToAria=function(f,h){var k=10,i=[16,0,11601],j=[16,28],d;if(!f)return a;else if(f.toLowerCase()==="win32")d=i;else if(f.toLowerCase()==="mac")d=j;else return c;if(!h)return a;for(var g=h.split("."),b=0;b<d.length&&b<g.length;b++){var e=parseInt(g[b],k);if(isNaN(e))return a;if(e<d[b])return c;if(e>d[b])return a}return a};b.prototype.isDirectToAriaEnabled=function(){var a=this;if(a.EnableDirectToAria===undefined||a.EnableDirectToAria===null){var c=void 0,b=void 0;if(OSF._OfficeAppFactory&&OSF._OfficeAppFactory.getHostInfo)c=OSF._OfficeAppFactory.getHostInfo()[f];if(window.external&&typeof window.external.GetContext!==e&&typeof window.external.GetContext().GetHostFullVersion!==e)b=window.external.GetContext().GetHostFullVersion();a.EnableDirectToAria=a.shouldSendDirectToAria(c,b)}return a.EnableDirectToAria};b.prototype.sendTelemetry=function(c,a){var e=1e3,d=b.EnableSendingTelemetryWithLegacyAria&&this.isDirectToAriaEnabled();d&&OSF.OUtil.loadScript(this.getAriaCDNLocation(),function(){try{if(!this.ALogger){var e="db334b301e7b474db5e0f02f07c51a47-a1b5bc36-1bbe-482f-a64a-c2d9cb606706-7439";this.ALogger=AWTLogManager.initialize(e)}var b=new AWTEventProperties;b.setName("Office.Extensibility.OfficeJS."+c);for(var d in a)d.toLowerCase()!=="table"&&b.setProperty(d,a[d]);var f=new Date;b.setProperty("Date",f.toISOString());this.ALogger.logEvent(b)}catch(g){}},e);b.EnableSendingTelemetryWithOTel&&r(c,a)};b.prototype.logData=function(a){if(this.isIUsageData(a))this.sendTelemetry(a["Table"],a["Fields"]);else this.sendTelemetry(a["Table"],a)};b.EnableSendingTelemetryWithOTel=c;b.EnableSendingTelemetryWithLegacyAria=a;return b}();w.AriaLogger=y})(OSFAriaLogger||(OSFAriaLogger={}));var OSFAppTelemetry;(function(d){var e=false,b=null,f=true,c="";"use strict";var a,h=OSF.OUtil.Guid.generateNewGuid(),k=c,w=new RegExp("^https?://store\\.office(ppe|-int)?\\.com/","i"),q="PRIVATE";d.enableTelemetry=f;var r=function(){function a(){}return a}();d.AppInfo=r;var j=function(){function a(b,a){this.name=b;this.handler=a}return a}(),n=function(){function a(){this.clientIDKey="Office API client";this.logIdSetKey="Office App Log Id Set"}a.prototype.getClientId=function(){var b=this,a=b.getValue(b.clientIDKey);if(!a||a.length<=0||a.length>40){a=OSF.OUtil.Guid.generateNewGuid();b.setValue(b.clientIDKey,a)}return a};a.prototype.saveLog=function(d,e){var b=this,a=b.getValue(b.logIdSetKey);a=(a&&a.length>0?a+";":c)+d;b.setValue(b.logIdSetKey,a);b.setValue(d,e)};a.prototype.enumerateLog=function(c,e){var a=this,d=a.getValue(a.logIdSetKey);if(d){var f=d.split(";");for(var h in f){var b=f[h],g=a.getValue(b);if(g){c&&c(b,g);e&&a.remove(b)}}e&&a.remove(a.logIdSetKey)}};a.prototype.getValue=function(d){var a=OSF.OUtil.getLocalStorage(),b=c;if(a)b=a.getItem(d);return b};a.prototype.setValue=function(c,b){var a=OSF.OUtil.getLocalStorage();a&&a.setItem(c,b)};a.prototype.remove=function(b){var a=OSF.OUtil.getLocalStorage();if(a)try{a.removeItem(b)}catch(c){}};return a}(),i=function(){function a(){}a.prototype.LogData=function(a){if(!d.enableTelemetry)return;try{OSFAriaLogger.AriaLogger.getInstance().logData(a)}catch(b){}};a.prototype.LogRawData=function(a){if(!d.enableTelemetry)return;try{OSFAriaLogger.AriaLogger.getInstance().logData(JSON.parse(a))}catch(b){}};return a}();function g(a){if(a)a=a.replace(/[{}]/g,c).toLowerCase();return a||c}function G(i){if(!d.enableTelemetry)return;if(a)return;a=new r;if(i.get_hostFullVersion())a.hostVersion=i.get_hostFullVersion();else a.hostVersion=i.get_appVersion();a.appId=m()?i.get_id():q;a.marketplaceType=i._marketplaceType;a.browser=window.navigator.userAgent;a.correlationId=g(i.get_correlationId());a.clientId=(new n).getClientId();a.appInstanceId=i.get_appInstanceId();if(a.appInstanceId){a.appInstanceId=g(a.appInstanceId);a.appInstanceId=o(i.get_id(),a.appInstanceId)}a.message=i.get_hostCustomMessage();a.officeJSVersion=OSF.ConstantNames.FileVersion;a.hostJSVersion="0.0.0.0";if(i._wacHostEnvironment)a.wacHostEnvironment=i._wacHostEnvironment;if(i._isFromWacAutomation!==undefined&&i._isFromWacAutomation!==b)a.isFromWacAutomation=i._isFromWacAutomation.toString().toLowerCase();var l=i.get_docUrl();a.docUrl=w.test(l)?l:c;var k=location.href;if(k)k=k.split("?")[0].split("#")[0];a.appURL=c;(function(i,a){var e,h,d;a.assetId=c;a.userId=c;try{e=decodeURIComponent(i);h=new DOMParser;d=h.parseFromString(e,"text/xml");var f=d.getElementsByTagName("t")[0].attributes.getNamedItem("cid"),g=d.getElementsByTagName("t")[0].attributes.getNamedItem("oid");if(f&&f.nodeValue)a.userId=f.nodeValue;else if(g&&g.nodeValue)a.userId=g.nodeValue;a.assetId=d.getElementsByTagName("t")[0].attributes.getNamedItem("aid").nodeValue}catch(j){}finally{e=b;d=b;h=b}})(i.get_eToken(),a);a.sessionId=h;typeof OTel!=="undefined"&&OTel.OTelLogger.initialize(a);(function(){var m=new Date,c=b,i=0,l=e,g=function(){if(document.hasFocus()){if(c==b)c=new Date}else if(c){i+=Math.abs((new Date).getTime()-c.getTime());c=b}},a=[];a.push(new j("focus",g));a.push(new j("blur",g));a.push(new j("focusout",g));a.push(new j("focusin",g));var k=function(){for(var e=0;e<a.length;e++)OSF.OUtil.removeEventListener(window,a[e].name,a[e].handler);a.length=0;if(!l){if(document.hasFocus()&&c){i+=Math.abs((new Date).getTime()-c.getTime());c=b}d.onAppClosed(Math.abs((new Date).getTime()-m.getTime()),i);l=f}};a.push(new j("beforeunload",k));a.push(new j("unload",k));for(var h=0;h<a.length;h++)OSF.OUtil.addEventListener(window,a[h].name,a[h].handler);g()})();d.onAppActivated()}d.initialize=G;function x(){if(!a)return;(new n).enumerateLog(function(b,a){return (new i).LogRawData(a)},f);var d=new OSFLog.AppActivatedUsageData;d.SessionId=h;d.AppId=a.appId;d.AssetId=a.assetId;d.AppURL=c;d.UserId=c;d.ClientId=a.clientId;d.Browser=a.browser;d.HostVersion=a.hostVersion;d.CorrelationId=g(a.correlationId);d.AppSizeWidth=window.innerWidth;d.AppSizeHeight=window.innerHeight;d.AppInstanceId=a.appInstanceId;d.Message=a.message;d.DocUrl=a.docUrl;d.OfficeJSVersion=a.officeJSVersion;d.HostJSVersion=a.hostJSVersion;if(a.wacHostEnvironment)d.WacHostEnvironment=a.wacHostEnvironment;if(a.isFromWacAutomation!==undefined&&a.isFromWacAutomation!==b)d.IsFromWacAutomation=a.isFromWacAutomation;(new i).LogData(d)}d.onAppActivated=x;function D(e,d,c,b){var a=new OSFLog.ScriptLoadUsageData;a.CorrelationId=g(b);a.SessionId=h;a.ScriptId=e;a.StartTime=d;a.ResponseTime=c;(new i).LogData(a)}d.onScriptDone=D;function H(c,d,f,e,j){if(!a)return;if(!B()||!t(d,c))return;var b=new OSFLog.APIUsageUsageData;b.CorrelationId=g(k);b.SessionId=h;b.APIType=c;b.APIID=d;b.Parameters=f;b.ResponseTime=e;b.ErrorType=j;(new i).LogData(b)}d.onCallDone=H;function C(h,d,f,g){var a=b;if(d)if(typeof d=="number")a=String(d);else if(typeof d==="object")for(var e in d){if(a!==b)a+=",";else a=c;if(typeof d[e]=="number")a+=String(d[e])}else a=c;OSF.AppTelemetry.onCallDone("method",h,a,f,g)}d.onMethodDone=C;function z(b,a){OSF.AppTelemetry.onCallDone("property",-1,b,a)}d.onPropertyDone=z;function y(b,e,f,d){var a=new OSFLog.CheckWACHostUsageData;a.isWacKnownHost=b;a.instanceId=e;a.hostType=f;a.hostPlatform=d;a.wacDomain=c;(new i).LogData(a)}d.onCheckWACHost=y;function F(c,a){OSF.AppTelemetry.onCallDone("event",c,b,0,a)}d.onEventDone=F;function A(d,e,a,c){OSF.AppTelemetry.onCallDone(d?"registerevent":"unregisterevent",e,b,a,c)}d.onRegisterDone=A;function E(d,c){if(!a)return;var b=new OSFLog.AppClosedUsageData;b.CorrelationId=g(k);b.SessionId=h;b.FocusTime=c;b.OpenTime=d;b.AppSizeFinalWidth=window.innerWidth;b.AppSizeFinalHeight=window.innerHeight;(new n).saveLog(h,b.SerializeRow())}d.onAppClosed=E;function s(a){k=g(a)}d.setOsfControlAppCorrelationId=s;function l(b,c){var a=new OSFLog.AppInitializationUsageData;a.CorrelationId=g(k);a.SessionId=h;a.SuccessCode=b?1:0;a.Message=c;(new i).LogData(a)}d.doAppInitializationLogging=l;function u(a){l(e,a)}d.logAppCommonMessage=u;function v(a){l(f,a)}d.logAppException=v;function B(){if(!OSF._OfficeAppFactory||!OSF._OfficeAppFactory.getHostInfo)return e;var a=OSF._OfficeAppFactory.getHostInfo();if(!a)return e;switch(a["hostType"]){case "outlook":switch(a["hostPlatform"]){case "mac":case "web":return f;default:return e}default:return e}}function t(b,a){if(a==="method")switch(b){case 3:case 4:case 38:case 37:case 10:case 12:return f;default:return e}return e}function m(){var b=(OSF._OfficeAppFactory.getHostInfo().flags&OSF.HostInfoFlags.PublicAddin)!=0;if(b)return b;if(!a)return e;var c=OSF._OfficeAppFactory.getHostInfo().hostPlatform,d=a.hostVersion;return p(c,d)}d.canSendAddinId=m;function o(b,a){if(!m()&&a===b)return q;return a}d.getCompliantAppInstanceId=o;function p(d,j){var c=e,i=/^(\d+)\.(\d+)\.(\d+)\.(\d+)$/,a=i.exec(j);if(a){var b=parseInt(a[1]),h=parseInt(a[2]),g=parseInt(a[3]);if(d=="win32"){if(b<16||b==16&&g<14225)c=f}else if(d=="mac")if(b<16||b==16&&(h<52||h==52&&g<808))c=f}return c}d._isComplianceExceptedHost=p;OSF.AppTelemetry=d})(OSFAppTelemetry||(OSFAppTelemetry={}));Microsoft.Office.WebExtension.EventType={};OSF.EventDispatch=function(c){var b=this;b._eventHandlers={};b._objectEventHandlers={};b._queuedEventsArgs={};if(c!=null)for(var d=0;d<c.length;d++){var a=c[d],e=a=="objectDeleted"||a=="objectSelectionChanged"||a=="objectDataChanged"||a=="contentControlAdded";if(!e)b._eventHandlers[a]=[];else b._objectEventHandlers[a]={};b._queuedEventsArgs[a]=[]}};OSF.EventDispatch.prototype={getSupportedEvents:function(){var a=[];for(var b in this._eventHandlers)a.push(b);for(var b in this._objectEventHandlers)a.push(b);return a},supportsEvent:function(b){for(var a in this._eventHandlers)if(b==a)return true;for(var a in this._objectEventHandlers)if(b==a)return true;return false},hasEventHandler:function(c,d){var a=this._eventHandlers[c];if(a&&a.length>0)for(var b=0;b<a.length;b++)if(a[b]===d)return true;return false},hasObjectEventHandler:function(d,e,f){var c=this._objectEventHandlers[d];if(c!=null)for(var a=c[e],b=0;a!=null&&b<a.length;b++)if(a[b]===f)return true;return false},addEventHandler:function(b,a){if(typeof a!="function")return false;var c=this._eventHandlers[b];if(c&&!this.hasEventHandler(b,a)){c.push(a);return true}else return false},addObjectEventHandler:function(d,b,c){if(typeof c!="function")return false;var a=this._objectEventHandlers[d];if(a&&!this.hasObjectEventHandler(d,b,c)){if(a[b]==null)a[b]=[];a[b].push(c);return true}return false},addEventHandlerAndFireQueuedEvent:function(a,e){var d=this._eventHandlers[a],c=d.length==0,b=this.addEventHandler(a,e);c&&b&&this.fireQueuedEvent(a);return b},removeEventHandler:function(c,d){var a=this._eventHandlers[c];if(a&&a.length>0)for(var b=0;b<a.length;b++)if(a[b]===d){a.splice(b,1);return true}return false},removeObjectEventHandler:function(d,e,f){var c=this._objectEventHandlers[d];if(c!=null)for(var a=c[e],b=0;a!=null&&b<a.length;b++)if(a[b]===f){a.splice(b,1);return true}return false},clearEventHandlers:function(a){if(typeof this._eventHandlers[a]!="undefined"&&this._eventHandlers[a].length>0){this._eventHandlers[a]=[];return true}return false},clearObjectEventHandlers:function(a,b){if(this._objectEventHandlers[a]!=null&&this._objectEventHandlers[a][b]!=null){this._objectEventHandlers[a][b]=[];return true}return false},getEventHandlerCount:function(a){return this._eventHandlers[a]!=undefined?this._eventHandlers[a].length:-1},getObjectEventHandlerCount:function(a,b){if(this._objectEventHandlers[a]==null||this._objectEventHandlers[a][b]==null)return 0;return this._objectEventHandlers[a][b].length},fireEvent:function(a){if(a.type==undefined)return false;var b=a.type;if(b&&this._eventHandlers[b]){for(var d=this._eventHandlers[b],c=0;c<d.length;c++)d[c](a);return true}else return false},fireObjectEvent:function(f,a){if(a.type==undefined)return false;var b=a.type;if(b&&this._objectEventHandlers[b]){var e=this._objectEventHandlers[b],c=e[f];if(c!=null){for(var d=0;d<c.length;d++)c[d](a);return true}}return false},fireOrQueueEvent:function(c){var b=this,a=c.type;if(a&&b._eventHandlers[a]){var d=b._eventHandlers[a],e=b._queuedEventsArgs[a];if(d.length==0)e.push(c);else b.fireEvent(c);return true}else return false},fireQueuedEvent:function(a){if(a&&this._eventHandlers[a]){var b=this._eventHandlers[a],c=this._queuedEventsArgs[a];if(b.length>0){var d=b[0];while(c.length>0){var e=c.shift();d(e)}return true}}return false},clearQueuedEvent:function(a){if(a&&this._eventHandlers[a]){var b=this._queuedEventsArgs[a];if(b)this._queuedEventsArgs[a]=[]}}};OSF.DDA.OMFactory=OSF.DDA.OMFactory||{};OSF.DDA.OMFactory.manufactureEventArgs=function(c,d,b){var h="hostPlatform",f="outlook",e="hostType",g=this,a;switch(c){case Microsoft.Office.WebExtension.EventType.DocumentSelectionChanged:a=new OSF.DDA.DocumentSelectionChangedEventArgs(d);break;case Microsoft.Office.WebExtension.EventType.BindingSelectionChanged:a=new OSF.DDA.BindingSelectionChangedEventArgs(g.manufactureBinding(b,d.document),b[OSF.DDA.PropertyDescriptors.Subset]);break;case Microsoft.Office.WebExtension.EventType.BindingDataChanged:a=new OSF.DDA.BindingDataChangedEventArgs(g.manufactureBinding(b,d.document));break;case Microsoft.Office.WebExtension.EventType.SettingsChanged:a=new OSF.DDA.SettingsChangedEventArgs(d);break;case Microsoft.Office.WebExtension.EventType.ActiveViewChanged:a=new OSF.DDA.ActiveViewChangedEventArgs(b);break;case Microsoft.Office.WebExtension.EventType.OfficeThemeChanged:a=new OSF.DDA.Theming.OfficeThemeChangedEventArgs(b);break;case Microsoft.Office.WebExtension.EventType.DocumentThemeChanged:a=new OSF.DDA.Theming.DocumentThemeChangedEventArgs(b);break;case Microsoft.Office.WebExtension.EventType.AppCommandInvoked:a=OSF.DDA.AppCommand.AppCommandInvokedEventArgs.create(b);break;case Microsoft.Office.WebExtension.EventType.ObjectDeleted:case Microsoft.Office.WebExtension.EventType.ObjectSelectionChanged:case Microsoft.Office.WebExtension.EventType.ObjectDataChanged:case Microsoft.Office.WebExtension.EventType.ContentControlAdded:a=new OSF.DDA.ObjectEventArgs(c,b[Microsoft.Office.WebExtension.Parameters.Id]);break;case Microsoft.Office.WebExtension.EventType.RichApiMessage:a=new OSF.DDA.RichApiMessageEventArgs(c,b);break;case Microsoft.Office.WebExtension.EventType.DataNodeInserted:a=new OSF.DDA.NodeInsertedEventArgs(g.manufactureDataNode(b[OSF.DDA.DataNodeEventProperties.NewNode]),b[OSF.DDA.DataNodeEventProperties.InUndoRedo]);break;case Microsoft.Office.WebExtension.EventType.DataNodeReplaced:a=new OSF.DDA.NodeReplacedEventArgs(g.manufactureDataNode(b[OSF.DDA.DataNodeEventProperties.OldNode]),g.manufactureDataNode(b[OSF.DDA.DataNodeEventProperties.NewNode]),b[OSF.DDA.DataNodeEventProperties.InUndoRedo]);break;case Microsoft.Office.WebExtension.EventType.DataNodeDeleted:a=new OSF.DDA.NodeDeletedEventArgs(g.manufactureDataNode(b[OSF.DDA.DataNodeEventProperties.OldNode]),g.manufactureDataNode(b[OSF.DDA.DataNodeEventProperties.NextSiblingNode]),b[OSF.DDA.DataNodeEventProperties.InUndoRedo]);break;case Microsoft.Office.WebExtension.EventType.TaskSelectionChanged:a=new OSF.DDA.TaskSelectionChangedEventArgs(d);break;case Microsoft.Office.WebExtension.EventType.ResourceSelectionChanged:a=new OSF.DDA.ResourceSelectionChangedEventArgs(d);break;case Microsoft.Office.WebExtension.EventType.ViewSelectionChanged:a=new OSF.DDA.ViewSelectionChangedEventArgs(d);break;case Microsoft.Office.WebExtension.EventType.DialogMessageReceived:a=new OSF.DDA.DialogEventArgs(b);break;case Microsoft.Office.WebExtension.EventType.DialogParentMessageReceived:a=new OSF.DDA.DialogParentEventArgs(b);break;case Microsoft.Office.WebExtension.EventType.ItemChanged:if(OSF._OfficeAppFactory.getHostInfo()[e]==f){a=new OSF.DDA.OlkItemSelectedChangedEventArgs(b);d.initialize(a["initialData"]);(OSF._OfficeAppFactory.getHostInfo()[h]=="win32"||OSF._OfficeAppFactory.getHostInfo()[h]=="mac")&&d.setCurrentItemNumber(a["itemNumber"].itemNumber)}else throw OsfMsAjaxFactory.msAjaxError.argument(Microsoft.Office.WebExtension.Parameters.EventType,OSF.OUtil.formatString(Strings.OfficeOM.L_NotSupportedEventType,c));break;case Microsoft.Office.WebExtension.EventType.RecipientsChanged:if(OSF._OfficeAppFactory.getHostInfo()[e]==f)a=new OSF.DDA.OlkRecipientsChangedEventArgs(b);else throw OsfMsAjaxFactory.msAjaxError.argument(Microsoft.Office.WebExtension.Parameters.EventType,OSF.OUtil.formatString(Strings.OfficeOM.L_NotSupportedEventType,c));break;case Microsoft.Office.WebExtension.EventType.AppointmentTimeChanged:if(OSF._OfficeAppFactory.getHostInfo()[e]==f)a=new OSF.DDA.OlkAppointmentTimeChangedEventArgs(b);else throw OsfMsAjaxFactory.msAjaxError.argument(Microsoft.Office.WebExtension.Parameters.EventType,OSF.OUtil.formatString(Strings.OfficeOM.L_NotSupportedEventType,c));break;case Microsoft.Office.WebExtension.EventType.RecurrenceChanged:if(OSF._OfficeAppFactory.getHostInfo()[e]==f)a=new OSF.DDA.OlkRecurrenceChangedEventArgs(b);else throw OsfMsAjaxFactory.msAjaxError.argument(Microsoft.Office.WebExtension.Parameters.EventType,OSF.OUtil.formatString(Strings.OfficeOM.L_NotSupportedEventType,c));break;case Microsoft.Office.WebExtension.EventType.AttachmentsChanged:if(OSF._OfficeAppFactory.getHostInfo()[e]==f)a=new OSF.DDA.OlkAttachmentsChangedEventArgs(b);else throw OsfMsAjaxFactory.msAjaxError.argument(Microsoft.Office.WebExtension.Parameters.EventType,OSF.OUtil.formatString(Strings.OfficeOM.L_NotSupportedEventType,c));break;case Microsoft.Office.WebExtension.EventType.EnhancedLocationsChanged:if(OSF._OfficeAppFactory.getHostInfo()[e]==f)a=new OSF.DDA.OlkEnhancedLocationsChangedEventArgs(b);else throw OsfMsAjaxFactory.msAjaxError.argument(Microsoft.Office.WebExtension.Parameters.EventType,OSF.OUtil.formatString(Strings.OfficeOM.L_NotSupportedEventType,c));break;case Microsoft.Office.WebExtension.EventType.InfobarClicked:if(OSF._OfficeAppFactory.getHostInfo()[e]==f)a=new OSF.DDA.OlkInfobarClickedEventArgs(b);else throw OsfMsAjaxFactory.msAjaxError.argument(Microsoft.Office.WebExtension.Parameters.EventType,OSF.OUtil.formatString(Strings.OfficeOM.L_NotSupportedEventType,c));break;default:throw OsfMsAjaxFactory.msAjaxError.argument(Microsoft.Office.WebExtension.Parameters.EventType,OSF.OUtil.formatString(Strings.OfficeOM.L_NotSupportedEventType,c))}return a};OSF.DDA.AsyncMethodNames.addNames({AddHandlerAsync:"addHandlerAsync",RemoveHandlerAsync:"removeHandlerAsync"});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.AddHandlerAsync,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.EventType,"enum":Microsoft.Office.WebExtension.EventType,verify:function(b,c,a){return a.supportsEvent(b)}},{name:Microsoft.Office.WebExtension.Parameters.Handler,types:["function"]}],supportedOptions:[],privateStateCallbacks:[]});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.RemoveHandlerAsync,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.EventType,"enum":Microsoft.Office.WebExtension.EventType,verify:function(b,c,a){return a.supportsEvent(b)}}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.Handler,value:{types:["function","object"],defaultValue:null}}],privateStateCallbacks:[]});Microsoft.Office.WebExtension.TableData=function(b,a){function c(a){if(a==null||a==undefined)return null;try{for(var b=OSF.DDA.DataCoercion.findArrayDimensionality(a,2);b<2;b++)a=[a];return a}catch(c){}}OSF.OUtil.defineEnumerableProperties(this,{headers:{"get":function(){return a},"set":function(b){a=c(b)}},rows:{"get":function(){return b},"set":function(a){b=a==null||OSF.OUtil.isArray(a)&&a.length==0?[]:c(a)}}});this.headers=a;this.rows=b};OSF.DDA.OMFactory=OSF.DDA.OMFactory||{};OSF.DDA.OMFactory.manufactureTableData=function(a){return new Microsoft.Office.WebExtension.TableData(a[OSF.DDA.TableDataProperties.TableRows],a[OSF.DDA.TableDataProperties.TableHeaders])};Microsoft.Office.WebExtension.CoercionType={Text:"text",Matrix:"matrix",Table:"table"};OSF.DDA.DataCoercion=function(){var a=null;return {findArrayDimensionality:function(c){if(OSF.OUtil.isArray(c)){for(var b=0,a=0;a<c.length;a++)b=Math.max(b,OSF.DDA.DataCoercion.findArrayDimensionality(c[a]));return b+1}else return 0},getCoercionDefaultForBinding:function(a){switch(a){case Microsoft.Office.WebExtension.BindingType.Matrix:return Microsoft.Office.WebExtension.CoercionType.Matrix;case Microsoft.Office.WebExtension.BindingType.Table:return Microsoft.Office.WebExtension.CoercionType.Table;case Microsoft.Office.WebExtension.BindingType.Text:default:return Microsoft.Office.WebExtension.CoercionType.Text}},getBindingDefaultForCoercion:function(a){switch(a){case Microsoft.Office.WebExtension.CoercionType.Matrix:return Microsoft.Office.WebExtension.BindingType.Matrix;case Microsoft.Office.WebExtension.CoercionType.Table:return Microsoft.Office.WebExtension.BindingType.Table;case Microsoft.Office.WebExtension.CoercionType.Text:case Microsoft.Office.WebExtension.CoercionType.Html:case Microsoft.Office.WebExtension.CoercionType.Ooxml:default:return Microsoft.Office.WebExtension.BindingType.Text}},determineCoercionType:function(b){if(b==a||b==undefined)return a;var c=a,d=typeof b;if(b.rows!==undefined)c=Microsoft.Office.WebExtension.CoercionType.Table;else if(OSF.OUtil.isArray(b))c=Microsoft.Office.WebExtension.CoercionType.Matrix;else if(d=="string"||d=="number"||d=="boolean"||OSF.OUtil.isDate(b))c=Microsoft.Office.WebExtension.CoercionType.Text;else throw OSF.DDA.ErrorCodeManager.errorCodes.ooeUnsupportedDataObject;return c},coerceData:function(b,c,a){a=a||OSF.DDA.DataCoercion.determineCoercionType(b);if(a&&a!=c){OSF.OUtil.writeProfilerMark(OSF.InternalPerfMarker.DataCoercionBegin);b=OSF.DDA.DataCoercion._coerceDataFromTable(c,OSF.DDA.DataCoercion._coerceDataToTable(b,a));OSF.OUtil.writeProfilerMark(OSF.InternalPerfMarker.DataCoercionEnd)}return b},_matrixToText:function(a){if(a.length==1&&a[0].length==1)return ""+a[0][0];for(var b="",c=0;c<a.length;c++)b+=a[c].join("\t")+"\n";return b.substring(0,b.length-1)},_textToMatrix:function(c){for(var a=c.split("\n"),b=0;b<a.length;b++)a[b]=a[b].split("\t");return a},_tableToText:function(c){var b="";if(c.headers!=a)b=OSF.DDA.DataCoercion._matrixToText([c.headers])+"\n";var d=OSF.DDA.DataCoercion._matrixToText(c.rows);if(d=="")b=b.substring(0,b.length-1);return b+d},_tableToMatrix:function(b){var c=b.rows;b.headers!=a&&c.unshift(b.headers);return c},_coerceDataFromTable:function(d,c){var b;switch(d){case Microsoft.Office.WebExtension.CoercionType.Table:b=c;break;case Microsoft.Office.WebExtension.CoercionType.Matrix:b=OSF.DDA.DataCoercion._tableToMatrix(c);break;case Microsoft.Office.WebExtension.CoercionType.SlideRange:b=a;if(OSF.DDA.OMFactory.manufactureSlideRange)b=OSF.DDA.OMFactory.manufactureSlideRange(OSF.DDA.DataCoercion._tableToText(c));if(b==a)b=OSF.DDA.DataCoercion._tableToText(c);break;case Microsoft.Office.WebExtension.CoercionType.Text:case Microsoft.Office.WebExtension.CoercionType.Html:case Microsoft.Office.WebExtension.CoercionType.Ooxml:default:b=OSF.DDA.DataCoercion._tableToText(c)}return b},_coerceDataToTable:function(b,c){if(c==undefined)c=OSF.DDA.DataCoercion.determineCoercionType(b);var a;switch(c){case Microsoft.Office.WebExtension.CoercionType.Table:a=b;break;case Microsoft.Office.WebExtension.CoercionType.Matrix:a=new Microsoft.Office.WebExtension.TableData(b);break;case Microsoft.Office.WebExtension.CoercionType.Text:case Microsoft.Office.WebExtension.CoercionType.Html:case Microsoft.Office.WebExtension.CoercionType.Ooxml:default:a=new Microsoft.Office.WebExtension.TableData(OSF.DDA.DataCoercion._textToMatrix(b))}return a}}}();OSF.DialogShownStatus={hasDialogShown:false,isWindowDialog:false};OSF.OUtil.augmentList(OSF.DDA.EventDescriptors,{DialogMessageReceivedEvent:"DialogMessageReceivedEvent"});OSF.OUtil.augmentList(Microsoft.Office.WebExtension.EventType,{DialogMessageReceived:"dialogMessageReceived",DialogEventReceived:"dialogEventReceived"});OSF.OUtil.augmentList(OSF.DDA.PropertyDescriptors,{MessageType:"messageType",MessageContent:"messageContent",MessageOrigin:"messageOrigin"});OSF.DDA.DialogEventType={};OSF.OUtil.augmentList(OSF.DDA.DialogEventType,{DialogClosed:"dialogClosed",NavigationFailed:"naviationFailed"});OSF.DDA.AsyncMethodNames.addNames({DisplayDialogAsync:"displayDialogAsync",DisplayModalDialogAsync:"displayModalDialogAsync",CloseAsync:"close"});OSF.DDA.SyncMethodNames.addNames({MessageParent:"messageParent",MessageChild:"messageChild",SendMessage:"sendMessage",AddMessageHandler:"addEventHandler"});OSF.DDA.UI.ParentUI=function(){var a;if(Microsoft.Office.WebExtension.EventType.DialogParentMessageReceived!=null)a=new OSF.EventDispatch([Microsoft.Office.WebExtension.EventType.DialogMessageReceived,Microsoft.Office.WebExtension.EventType.DialogEventReceived,Microsoft.Office.WebExtension.EventType.DialogParentMessageReceived]);else a=new OSF.EventDispatch([Microsoft.Office.WebExtension.EventType.DialogMessageReceived,Microsoft.Office.WebExtension.EventType.DialogEventReceived]);if(Microsoft.Office.WebExtension.FeatureGates&&Microsoft.Office.WebExtension.FeatureGates["OfficeJsDialogRefactor"]){var b=this,e=function(c,d){!b[c]&&OSF.OUtil.defineEnumerableProperty(b,c,{value:function(){var c=OSF._OfficeAppFactory.getHostFacade()[OSF.DDA.DispIdHost.Methods.OpenDialog];c(arguments,a,b,d)}})};e(OSF.DDA.AsyncMethodNames.DisplayDialogAsync.displayName,false);Microsoft.Office.WebExtension.FeatureGates["ModalWebDialogAPI"]&&e(OSF.DDA.AsyncMethodNames.DisplayModalDialogAsync.displayName,true)}else{var d=OSF.DDA.AsyncMethodNames.DisplayDialogAsync.displayName,c=this;!c[d]&&OSF.OUtil.defineEnumerableProperty(c,d,{value:function(){var b=OSF._OfficeAppFactory.getHostFacade()[OSF.DDA.DispIdHost.Methods.OpenDialog];b(arguments,a,c,false)}})}OSF.OUtil.finalizeProperties(this)};OSF.DDA.UI.ChildUI=function(d){var b=OSF.DDA.SyncMethodNames.MessageParent.displayName,a=this;!a[b]&&OSF.OUtil.defineEnumerableProperty(a,b,{value:function(){var b=OSF._OfficeAppFactory.getHostFacade()[OSF.DDA.DispIdHost.Methods.MessageParent];return b(arguments,a)}});var c=OSF.DDA.SyncMethodNames.AddMessageHandler.displayName;!a[c]&&typeof OSF.DialogParentMessageEventDispatch!="undefined"&&OSF.DDA.DispIdHost.addEventSupport(a,OSF.DialogParentMessageEventDispatch,d);OSF.OUtil.finalizeProperties(this)};OSF.DialogHandler=function(){};OSF.DDA.DialogEventArgs=function(a){if(a[OSF.DDA.PropertyDescriptors.MessageType]==OSF.DialogMessageType.DialogMessageReceived)OSF.OUtil.defineEnumerableProperties(this,{type:{value:Microsoft.Office.WebExtension.EventType.DialogMessageReceived},message:{value:a[OSF.DDA.PropertyDescriptors.MessageContent]},origin:{value:a[OSF.DDA.PropertyDescriptors.MessageOrigin]}});else OSF.OUtil.defineEnumerableProperties(this,{type:{value:Microsoft.Office.WebExtension.EventType.DialogEventReceived},error:{value:a[OSF.DDA.PropertyDescriptors.MessageType]}})};OSF.DDA.DialogParentEventArgs=function(a){OSF.OUtil.defineEnumerableProperties(this,{type:{value:Microsoft.Office.WebExtension.EventType.DialogParentMessageReceived},message:{value:a[OSF.DDA.PropertyDescriptors.MessageContent]},origin:{value:a[OSF.DDA.PropertyDescriptors.MessageOrigin]}})};var DialogApiManager=function(){var d=false,c="boolean",a=true;function b(){}b.defineApi=function(d,c){var b=OSF.DDA.AsyncMethodCalls;b.define({method:d,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.Url,types:["string"]}],supportedOptions:c,privateStateCallbacks:[],onSucceeded:function(d){var i=d[Microsoft.Office.WebExtension.Parameters.Id],c=d[Microsoft.Office.WebExtension.Parameters.Data],b=new OSF.DialogHandler,f=OSF.DDA.AsyncMethodNames.CloseAsync.displayName;OSF.OUtil.defineEnumerableProperty(b,f,{value:function(){var a=OSF._OfficeAppFactory.getHostFacade()[OSF.DDA.DispIdHost.Methods.CloseDialog];a(arguments,i,c,b)}});var h=OSF.DDA.SyncMethodNames.AddMessageHandler.displayName;OSF.OUtil.defineEnumerableProperty(b,h,{value:function(){var d=OSF.DDA.SyncMethodCalls[OSF.DDA.SyncMethodNames.AddMessageHandler.id],a=d.verifyAndExtractCall(arguments,b,c),e=a[Microsoft.Office.WebExtension.Parameters.EventType],f=a[Microsoft.Office.WebExtension.Parameters.Handler];return c.addEventHandlerAndFireQueuedEvent(e,f)}});if(OSF.DDA.UI.EnableSendMessageDialogAPI===a){var g=OSF.DDA.SyncMethodNames.SendMessage.displayName;OSF.OUtil.defineEnumerableProperty(b,g,{value:function(){var a=OSF._OfficeAppFactory.getHostFacade()[OSF.DDA.DispIdHost.Methods.SendMessage];return a(arguments,c,b)}})}if(OSF.DDA.UI.EnableMessageChildDialogAPI===a){var e=OSF.DDA.SyncMethodNames.MessageChild.displayName;OSF.OUtil.defineEnumerableProperty(b,e,{value:function(){var a=OSF._OfficeAppFactory.getHostFacade()[OSF.DDA.DispIdHost.Methods.SendMessage];return a(arguments,c,b)}})}return b},checkCallArgs:function(b){if(b[Microsoft.Office.WebExtension.Parameters.Width]<=0)b[Microsoft.Office.WebExtension.Parameters.Width]=1;if(!b[Microsoft.Office.WebExtension.Parameters.UseDeviceIndependentPixels]&&b[Microsoft.Office.WebExtension.Parameters.Width]>100)b[Microsoft.Office.WebExtension.Parameters.Width]=99;if(b[Microsoft.Office.WebExtension.Parameters.Height]<=0)b[Microsoft.Office.WebExtension.Parameters.Height]=1;if(!b[Microsoft.Office.WebExtension.Parameters.UseDeviceIndependentPixels]&&b[Microsoft.Office.WebExtension.Parameters.Height]>100)b[Microsoft.Office.WebExtension.Parameters.Height]=99;if(!b[Microsoft.Office.WebExtension.Parameters.RequireHTTPs])b[Microsoft.Office.WebExtension.Parameters.RequireHTTPs]=a;return b}})};b.messageChildRichApiBridge=function(){if(OSF.DDA.UI.EnableMessageChildDialogAPI===a){var b=OSF._OfficeAppFactory.getHostFacade()[OSF.DDA.DispIdHost.Methods.SendMessage];return b(arguments,null,null)}};b.initOnce=function(){b.defineApi(OSF.DDA.AsyncMethodNames.DisplayDialogAsync,b.displayDialogAsyncApiSupportedOptions);b.defineApi(OSF.DDA.AsyncMethodNames.DisplayModalDialogAsync,b.displayModalDialogAsyncApiSupportedOptions)};b.displayDialogAsyncApiSupportedOptions=[{name:Microsoft.Office.WebExtension.Parameters.Width,value:{types:["number"],defaultValue:99}},{name:Microsoft.Office.WebExtension.Parameters.Height,value:{types:["number"],defaultValue:99}},{name:Microsoft.Office.WebExtension.Parameters.RequireHTTPs,value:{types:[c],defaultValue:a}},{name:Microsoft.Office.WebExtension.Parameters.DisplayInIframe,value:{types:[c],defaultValue:d}},{name:Microsoft.Office.WebExtension.Parameters.HideTitle,value:{types:[c],defaultValue:d}},{name:Microsoft.Office.WebExtension.Parameters.UseDeviceIndependentPixels,value:{types:[c],defaultValue:d}},{name:Microsoft.Office.WebExtension.Parameters.PromptBeforeOpen,value:{types:[c],defaultValue:a}},{name:Microsoft.Office.WebExtension.Parameters.EnforceAppDomain,value:{types:[c],defaultValue:a}},{name:Microsoft.Office.WebExtension.Parameters.UrlNoHostInfo,value:{types:[c],defaultValue:d}}];b.displayModalDialogAsyncApiSupportedOptions=b.displayDialogAsyncApiSupportedOptions.concat([{name:"abortWhenParentIsMinimized",value:{types:[c],defaultValue:d}},{name:"abortWhenDocIsInactive",value:{types:[c],defaultValue:d}}]);return b}();DialogApiManager.initOnce();OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.CloseAsync,requiredArguments:[],supportedOptions:[],privateStateCallbacks:[]});OSF.DDA.SyncMethodCalls.define({method:OSF.DDA.SyncMethodNames.MessageParent,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.MessageToParent,types:["string","number","boolean"]}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.TargetOrigin,value:{types:["string"],defaultValue:""}}]});OSF.DDA.SyncMethodCalls.define({method:OSF.DDA.SyncMethodNames.AddMessageHandler,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.EventType,"enum":Microsoft.Office.WebExtension.EventType,verify:function(b,c,a){return a.supportsEvent(b)}},{name:Microsoft.Office.WebExtension.Parameters.Handler,types:["function"]}],supportedOptions:[]});OSF.DDA.SyncMethodCalls.define({method:OSF.DDA.SyncMethodNames.SendMessage,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.MessageContent,types:["string"]}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.TargetOrigin,value:{types:["string"],defaultValue:""}}],privateStateCallbacks:[]});OSF.DDA.SafeArray.Delegate.openDialog=function(a){try{a.onCalling&&a.onCalling();var c=OSF.DDA.SafeArray.Delegate._getOnAfterRegisterEvent(true,a);OSF.ClientHostController.openDialog(a.dispId,a.targetId,function(c,b){a.onEvent&&a.onEvent(b);OSF.AppTelemetry&&OSF.AppTelemetry.onEventDone(a.dispId)},c)}catch(b){OSF.DDA.SafeArray.Delegate._onException(b,a)}};OSF.DDA.SafeArray.Delegate.closeDialog=function(a){a.onCalling&&a.onCalling();var c=OSF.DDA.SafeArray.Delegate._getOnAfterRegisterEvent(false,a);try{OSF.ClientHostController.closeDialog(a.dispId,a.targetId,c)}catch(b){OSF.DDA.SafeArray.Delegate._onException(b,a)}};OSF.DDA.SafeArray.Delegate.messageParent=function(a){try{a.onCalling&&a.onCalling();var d=(new Date).getTime(),b=OSF.ClientHostController.messageParent(a.hostCallArgs);a.onReceiving&&a.onReceiving();OSF.AppTelemetry&&OSF.AppTelemetry.onMethodDone(a.dispId,a.hostCallArgs,Math.abs((new Date).getTime()-d),b);return b}catch(c){return OSF.DDA.SafeArray.Delegate._onExceptionSyncMethod(c)}};OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.EventDispId.dispidDialogMessageReceivedEvent,fromHost:[{name:OSF.DDA.EventDescriptors.DialogMessageReceivedEvent,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}],isComplexType:true});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.EventDescriptors.DialogMessageReceivedEvent,fromHost:[{name:OSF.DDA.PropertyDescriptors.MessageType,value:0},{name:OSF.DDA.PropertyDescriptors.MessageContent,value:1},{name:OSF.DDA.PropertyDescriptors.MessageOrigin,value:2}],isComplexType:true});OSF.DDA.SafeArray.Delegate.sendMessage=function(a){try{a.onCalling&&a.onCalling();var d=(new Date).getTime(),c=OSF.ClientHostController.sendMessage(a.hostCallArgs);a.onReceiving&&a.onReceiving();return c}catch(b){return OSF.DDA.SafeArray.Delegate._onExceptionSyncMethod(b)}};OSF.DDA.AsyncMethodNames.addNames({OpenBrowserWindow:"openBrowserWindow"});OSF.DDA.OpenBrowser=function(){};OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.OpenBrowserWindow,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.Url,types:["string"]}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.Reserved,value:{types:["number"],defaultValue:0}}],privateStateCallbacks:[]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidOpenBrowserWindow,toHost:[{name:Microsoft.Office.WebExtension.Parameters.Reserved,value:0},{name:Microsoft.Office.WebExtension.Parameters.Url,value:1}]});OSF.DDA.AsyncMethodNames.addNames({ExecuteFeature:"executeFeatureAsync",QueryFeature:"queryFeatureAsync"});OSF.OUtil.augmentList(OSF.DDA.PropertyDescriptors,{FeatureProperties:"FeatureProperties",TcidEnabled:"TcidEnabled",TcidVisible:"TcidVisible"});OSF.DDA.ExecuteFeature=function(){};OSF.DDA.QueryFeature=function(){};OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.ExecuteFeature,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.Tcid,types:["number"]}],privateStateCallbacks:[]});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.QueryFeature,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.Tcid,types:["number"]}],privateStateCallbacks:[]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.PropertyDescriptors.FeatureProperties,fromHost:[{name:OSF.DDA.PropertyDescriptors.TcidEnabled,value:0},{name:OSF.DDA.PropertyDescriptors.TcidVisible,value:1}],isComplexType:true});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidExecuteFeature,toHost:[{name:Microsoft.Office.WebExtension.Parameters.Tcid,value:0}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidQueryFeature,fromHost:[{name:OSF.DDA.PropertyDescriptors.FeatureProperties,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}],toHost:[{name:Microsoft.Office.WebExtension.Parameters.Tcid,value:0}]});Microsoft.Office.WebExtension.ProjectViewTypes={Gantt:1,NetworkDiagram:2,TaskDiagram:3,TaskForm:4,TaskSheet:5,ResourceForm:6,ResourceSheet:7,ResourceGraph:8,TeamPlanner:9,TaskDetails:10,TaskNameForm:11,ResourceNames:12,Calendar:13,TaskUsage:14,ResourceUsage:15,Timeline:16,Drawing:18,ResourcePlan:19};OSF.OUtil.redefineList(Microsoft.Office.WebExtension.CoercionType,{Text:"text"});OSF.OUtil.redefineList(Microsoft.Office.WebExtension.ValueFormat,{Unformatted:"unformatted"});OSF.OUtil.augmentList(Microsoft.Office.WebExtension.EventType,{TaskSelectionChanged:"taskSelectionChanged",ResourceSelectionChanged:"resourceSelectionChanged",ViewSelectionChanged:"viewSelectionChanged"});OSF.DDA.AsyncMethodNames.addNames({GetSelectedDataAsync:"getSelectedDataAsync",SetSelectedDataAsync:"setSelectedDataAsync"});(function(){var c=false,b="boolean",a="number";function d(b,d,c){var a=b[Microsoft.Office.WebExtension.Parameters.Data];if(OSF.DDA.TableDataProperties&&a&&(a[OSF.DDA.TableDataProperties.TableRows]!=undefined||a[OSF.DDA.TableDataProperties.TableHeaders]!=undefined))a=OSF.DDA.OMFactory.manufactureTableData(a);a=OSF.DDA.DataCoercion.coerceData(a,c[Microsoft.Office.WebExtension.Parameters.CoercionType]);return a==undefined?null:a}OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetSelectedDataAsync,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.CoercionType,"enum":Microsoft.Office.WebExtension.CoercionType}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.ValueFormat,value:{"enum":Microsoft.Office.WebExtension.ValueFormat,defaultValue:Microsoft.Office.WebExtension.ValueFormat.Unformatted}},{name:Microsoft.Office.WebExtension.Parameters.FilterType,value:{"enum":Microsoft.Office.WebExtension.FilterType,defaultValue:Microsoft.Office.WebExtension.FilterType.All}}],privateStateCallbacks:[],onSucceeded:d});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.SetSelectedDataAsync,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.Data,types:["string","object",a,b]}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.CoercionType,value:{"enum":Microsoft.Office.WebExtension.CoercionType,calculate:function(a){return OSF.DDA.DataCoercion.determineCoercionType(a[Microsoft.Office.WebExtension.Parameters.Data])}}},{name:Microsoft.Office.WebExtension.Parameters.ImageLeft,value:{types:[a,b],defaultValue:c}},{name:Microsoft.Office.WebExtension.Parameters.ImageTop,value:{types:[a,b],defaultValue:c}},{name:Microsoft.Office.WebExtension.Parameters.ImageWidth,value:{types:[a,b],defaultValue:c}},{name:Microsoft.Office.WebExtension.Parameters.ImageHeight,value:{types:[a,b],defaultValue:c}}],privateStateCallbacks:[]})})();OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetSelectedDataMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.Data,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}],toHost:[{name:Microsoft.Office.WebExtension.Parameters.CoercionType,value:0},{name:Microsoft.Office.WebExtension.Parameters.ValueFormat,value:1},{name:Microsoft.Office.WebExtension.Parameters.FilterType,value:2}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidSetSelectedDataMethod,toHost:[{name:Microsoft.Office.WebExtension.Parameters.CoercionType,value:0},{name:Microsoft.Office.WebExtension.Parameters.Data,value:1},{name:Microsoft.Office.WebExtension.Parameters.ImageLeft,value:2},{name:Microsoft.Office.WebExtension.Parameters.ImageTop,value:3},{name:Microsoft.Office.WebExtension.Parameters.ImageWidth,value:4},{name:Microsoft.Office.WebExtension.Parameters.ImageHeight,value:5}]});OSF.DDA.SettingsManager={SerializedSettings:"serializedSettings",RefreshingSettings:"refreshingSettings",DateJSONPrefix:"Date(",DataJSONSuffix:")",serializeSettings:function(a){return OSF.OUtil.serializeSettings(a)},deserializeSettings:function(a){return OSF.OUtil.deserializeSettings(a)}};OSF.DDA.Settings=function(a){var b="name";a=a||{};var c=function(d){var b=OSF.OUtil.getSessionStorage();if(b){var a=OSF.DDA.SettingsManager.serializeSettings(d),c=JSON?JSON.stringify(a):Sys.Serialization.JavaScriptSerializer.serialize(a);b.setItem(OSF._OfficeAppFactory.getCachedSessionSettingsKey(),c)}};OSF.OUtil.defineEnumerableProperties(this,{"get":{value:function(e){var d=Function._validateParams(arguments,[{name:b,type:String,mayBeNull:false}]);if(d)throw d;var c=a[e];return typeof c==="undefined"?null:c}},"set":{value:function(f,e){var d=Function._validateParams(arguments,[{name:b,type:String,mayBeNull:false},{name:"value",mayBeNull:true}]);if(d)throw d;a[f]=e;c(a)}},remove:{value:function(e){var d=Function._validateParams(arguments,[{name:b,type:String,mayBeNull:false}]);if(d)throw d;delete a[e];c(a)}}});OSF.DDA.DispIdHost.addAsyncMethods(this,[OSF.DDA.AsyncMethodNames.SaveAsync],a)};OSF.DDA.RefreshableSettings=function(a){OSF.DDA.RefreshableSettings.uber.constructor.call(this,a);OSF.DDA.DispIdHost.addAsyncMethods(this,[OSF.DDA.AsyncMethodNames.RefreshAsync],a);OSF.DDA.DispIdHost.addEventSupport(this,new OSF.EventDispatch([Microsoft.Office.WebExtension.EventType.SettingsChanged]))};OSF.OUtil.extend(OSF.DDA.RefreshableSettings,OSF.DDA.Settings);OSF.OUtil.augmentList(Microsoft.Office.WebExtension.EventType,{SettingsChanged:"settingsChanged"});OSF.DDA.SettingsChangedEventArgs=function(a){OSF.OUtil.defineEnumerableProperties(this,{type:{value:Microsoft.Office.WebExtension.EventType.SettingsChanged},settings:{value:a}})};OSF.DDA.AsyncMethodNames.addNames({RefreshAsync:"refreshAsync",SaveAsync:"saveAsync"});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.RefreshAsync,requiredArguments:[],supportedOptions:[],privateStateCallbacks:[{name:OSF.DDA.SettingsManager.RefreshingSettings,value:function(b,a){return a}}],onSucceeded:function(d,a,e){var f=d[OSF.DDA.SettingsManager.SerializedSettings],c=OSF.DDA.SettingsManager.deserializeSettings(f),g=e[OSF.DDA.SettingsManager.RefreshingSettings];for(var b in g)a.remove(b);for(var b in c)a.set(b,c[b]);return a}});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.SaveAsync,requiredArguments:[],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.OverwriteIfStale,value:{types:["boolean"],defaultValue:true}}],privateStateCallbacks:[{name:OSF.DDA.SettingsManager.SerializedSettings,value:function(b,a){return OSF.DDA.SettingsManager.serializeSettings(a)}}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidLoadSettingsMethod,fromHost:[{name:OSF.DDA.SettingsManager.SerializedSettings,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidSaveSettingsMethod,toHost:[{name:OSF.DDA.SettingsManager.SerializedSettings,value:OSF.DDA.SettingsManager.SerializedSettings},{name:Microsoft.Office.WebExtension.Parameters.OverwriteIfStale,value:Microsoft.Office.WebExtension.Parameters.OverwriteIfStale}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.EventDispId.dispidSettingsChangedEvent});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:Microsoft.Office.WebExtension.Parameters.CoercionType,toHost:[{name:Microsoft.Office.WebExtension.CoercionType.Text,value:0},{name:Microsoft.Office.WebExtension.CoercionType.Matrix,value:1},{name:Microsoft.Office.WebExtension.CoercionType.Table,value:2}]});Microsoft.Office.WebExtension.ProjectTaskFields={ActualCost:0,ActualDuration:1,ActualFinish:2,ActualOvertimeCost:3,ActualOvertimeWork:4,ActualStart:5,ActualWork:6,Text1:7,Text10:8,Finish10:9,Start10:10,Text11:11,Text12:12,Text13:13,Text14:14,Text15:15,Text16:16,Text17:17,Text18:18,Text19:19,Finish1:20,Start1:21,Text2:22,Text20:23,Text21:24,Text22:25,Text23:26,Text24:27,Text25:28,Text26:29,Text27:30,Text28:31,Text29:32,Finish2:33,Start2:34,Text3:35,Text30:36,Finish3:37,Start3:38,Text4:39,Finish4:40,Start4:41,Text5:42,Finish5:43,Start5:44,Text6:45,Finish6:46,Start6:47,Text7:48,Finish7:49,Start7:50,Text8:51,Finish8:52,Start8:53,Text9:54,Finish9:55,Start9:56,Baseline10BudgetCost:57,Baseline10BudgetWork:58,Baseline10Cost:59,Baseline10Duration:60,Baseline10Finish:61,Baseline10FixedCost:62,Baseline10FixedCostAccrual:63,Baseline10Start:64,Baseline10Work:65,Baseline1BudgetCost:66,Baseline1BudgetWork:67,Baseline1Cost:68,Baseline1Duration:69,Baseline1Finish:70,Baseline1FixedCost:71,Baseline1FixedCostAccrual:72,Baseline1Start:73,Baseline1Work:74,Baseline2BudgetCost:75,Baseline2BudgetWork:76,Baseline2Cost:77,Baseline2Duration:78,Baseline2Finish:79,Baseline2FixedCost:80,Baseline2FixedCostAccrual:81,Baseline2Start:82,Baseline2Work:83,Baseline3BudgetCost:84,Baseline3BudgetWork:85,Baseline3Cost:86,Baseline3Duration:87,Baseline3Finish:88,Baseline3FixedCost:89,Baseline3FixedCostAccrual:90,Basline3Start:91,Baseline3Work:92,Baseline4BudgetCost:93,Baseline4BudgetWork:94,Baseline4Cost:95,Baseline4Duration:96,Baseline4Finish:97,Baseline4FixedCost:98,Baseline4FixedCostAccrual:99,Baseline4Start:100,Baseline4Work:101,Baseline5BudgetCost:102,Baseline5BudgetWork:103,Baseline5Cost:104,Baseline5Duration:105,Baseline5Finish:106,Baseline5FixedCost:107,Baseline5FixedCostAccrual:108,Baseline5Start:109,Baseline5Work:110,Baseline6BudgetCost:111,Baseline6BudgetWork:112,Baseline6Cost:113,Baseline6Duration:114,Baseline6Finish:115,Baseline6FixedCost:116,Baseline6FixedCostAccrual:117,Baseline6Start:118,Baseline6Work:119,Baseline7BudgetCost:120,Baseline7BudgetWork:121,Baseline7Cost:122,Baseline7Duration:123,Baseline7Finish:124,Baseline7FixedCost:125,Baseline7FixedCostAccrual:126,Baseline7Start:127,Baseline7Work:128,Baseline8BudgetCost:129,Baseline8BudgetWork:130,Baseline8Cost:131,Baseline8Duration:132,Baseline8Finish:133,Baseline8FixedCost:134,Baseline8FixedCostAccrual:135,Baseline8Start:136,Baseline8Work:137,Baseline9BudgetCost:138,Baseline9BudgetWork:139,Baseline9Cost:140,Baseline9Duration:141,Baseline9Finish:142,Baseline9FixedCost:143,Baseline9FixedCostAccrual:144,Baseline9Start:145,Baseline9Work:146,BaselineBudgetCost:147,BaselineBudgetWork:148,BaselineCost:149,BaselineDuration:150,BaselineFinish:151,BaselineFixedCost:152,BaselineFixedCostAccrual:153,BaselineStart:154,BaselineWork:155,BudgetCost:156,BudgetWork:157,TaskCalendarGUID:158,ConstraintDate:159,ConstraintType:160,Cost1:161,Cost10:162,Cost2:163,Cost3:164,Cost4:165,Cost5:166,Cost6:167,Cost7:168,Cost8:169,Cost9:170,Date1:171,Date10:172,Date2:173,Date3:174,Date4:175,Date5:176,Date6:177,Date7:178,Date8:179,Date9:180,Deadline:181,Duration1:182,Duration10:183,Duration2:184,Duration3:185,Duration4:186,Duration5:187,Duration6:188,Duration7:189,Duration8:190,Duration9:191,Duration:192,EarnedValueMethod:193,FinishSlack:194,FixedCost:195,FixedCostAccrual:196,Flag10:197,Flag1:198,Flag11:199,Flag12:200,Flag13:201,Flag14:202,Flag15:203,Flag16:204,Flag17:205,Flag18:206,Flag19:207,Flag2:208,Flag20:209,Flag3:210,Flag4:211,Flag5:212,Flag6:213,Flag7:214,Flag8:215,Flag9:216,FreeSlack:217,HasRollupSubTasks:218,ID:219,Name:220,Notes:221,Number1:222,Number10:223,Number11:224,Number12:225,Number13:226,Number14:227,Number15:228,Number16:229,Number17:230,Number18:231,Number19:232,Number2:233,Number20:234,Number3:235,Number4:236,Number5:237,Number6:238,Number7:239,Number8:240,Number9:241,ScheduledDuration:242,ScheduledFinish:243,ScheduledStart:244,OutlineLevel:245,OvertimeCost:246,OvertimeWork:247,PercentComplete:248,PercentWorkComplete:249,Predecessors:250,PreleveledFinish:251,PreleveledStart:252,Priority:253,Active:254,Critical:255,Milestone:256,Overallocated:257,IsRollup:258,Summary:259,RegularWork:260,RemainingCost:261,RemainingDuration:262,RemainingOvertimeCost:263,RemainingWork:264,ResourceNames:265,ResourceNames_deprecated:266,Cost:267,Finish:268,Start:269,Work:270,StartSlack:271,Status:272,Successors:273,StatusManager:274,TotalSlack:275,TaskGUID:276,Type:277,WBS:278,WBSPREDECESSORS:279,WBSSUCCESSORS:280,WSSID:281,FreeformDuration:282,FreeformFinish:283,FreeformStart:284};Microsoft.Office.WebExtension.ProjectResourceFields={Accrual:0,ActualCost:1,ActualOvertimeCost:2,ActualOvertimeWork:3,ActualOvertimeWorkProtected:4,ActualWork:5,ActualWorkProtected:6,BaseCalendar:7,Baseline10BudgetCost:8,Baseline10BudgetWork:9,Baseline10Cost:10,Baseline10Work:11,Baseline1BudgetCost:12,Baseline1BudgetWork:13,Baseline1Cost:14,Baseline1Work:15,Baseline2BudgetCost:16,Baseline2BudgetWork:17,Baseline2Cost:18,Baseline2Work:19,Baseline3BudgetCost:20,Baseline3BudgetWork:21,Baseline3Cost:22,Baseline3Work:23,Baseline4BudgetCost:24,Baseline4BudgetWork:25,Baseline4Cost:26,Baseline4Work:27,Baseline5BudgetCost:28,Baseline5BudgetWork:29,Baseline5Cost:30,Baseline5Work:31,Baseline6BudgetCost:32,Baseline6BudgetWork:33,Baseline6Cost:34,Baseline6Work:35,Baseline7BudgetCost:36,Baseline7BudgetWork:37,Baseline7Cost:38,Baseline7Work:39,Baseline8BudgetCost:40,Baseline8BudgetWork:41,Baseline8Cost:42,Baseline8Work:43,Baseline9BudgetCost:44,Baseline9BudgetWork:45,Baseline9Cost:46,Baseline9Work:47,BaselineBudgetCost:48,BaselineBudgetWork:49,BaselineCost:50,BaselineWork:51,BudgetCost:52,BudgetWork:53,ResourceCalendarGUID:54,Code:55,Cost1:56,Cost10:57,Cost2:58,Cost3:59,Cost4:60,Cost5:61,Cost6:62,Cost7:63,Cost8:64,Cost9:65,ResourceCreationDate:66,Date1:67,Date10:68,Date2:69,Date3:70,Date4:71,Date5:72,Date6:73,Date7:74,Date8:75,Date9:76,Duration1:77,Duration10:78,Duration2:79,Duration3:80,Duration4:81,Duration5:82,Duration6:83,Duration7:84,Duration8:85,Duration9:86,Email:87,End:88,Finish1:89,Finish10:90,Finish2:91,Finish3:92,Finish4:93,Finish5:94,Finish6:95,Finish7:96,Finish8:97,Finish9:98,Flag10:99,Flag1:100,Flag11:101,Flag12:102,Flag13:103,Flag14:104,Flag15:105,Flag16:106,Flag17:107,Flag18:108,Flag19:109,Flag2:110,Flag20:111,Flag3:112,Flag4:113,Flag5:114,Flag6:115,Flag7:116,Flag8:117,Flag9:118,Group:119,Units:120,Name:121,Notes:122,Number1:123,Number10:124,Number11:125,Number12:126,Number13:127,Number14:128,Number15:129,Number16:130,Number17:131,Number18:132,Number19:133,Number2:134,Number20:135,Number3:136,Number4:137,Number5:138,Number6:139,Number7:140,Number8:141,Number9:142,OvertimeCost:143,OvertimeRate:144,OvertimeWork:145,PercentWorkComplete:146,CostPerUse:147,Generic:148,OverAllocated:149,RegularWork:150,RemainingCost:151,RemainingOvertimeCost:152,RemainingOvertimeWork:153,RemainingWork:154,ResourceGUID:155,Cost:156,Work:157,Start:158,Start1:159,Start10:160,Start2:161,Start3:162,Start4:163,Start5:164,Start6:165,Start7:166,Start8:167,Start9:168,StandardRate:169,Text1:170,Text10:171,Text11:172,Text12:173,Text13:174,Text14:175,Text15:176,Text16:177,Text17:178,Text18:179,Text19:180,Text2:181,Text20:182,Text21:183,Text22:184,Text23:185,Text24:186,Text25:187,Text26:188,Text27:189,Text28:190,Text29:191,Text3:192,Text30:193,Text4:194,Text5:195,Text6:196,Text7:197,Text8:198,Text9:199};Microsoft.Office.WebExtension.ProjectProjectFields={CurrencyDigits:0,CurrencySymbol:1,CurrencySymbolPosition:2,DurationUnits:3,GUID:4,Finish:5,Start:6,ReadOnly:7,VERSION:8,WorkUnits:9,ProjectServerUrl:10,WSSUrl:11,WSSList:12,Name:13,CurrentUser:14};OSF.DDA.ProjectDocument=function(b){OSF.DDA.ProjectDocument.uber.constructor.call(this,b);var a=OSF.DDA.AsyncMethodNames;OSF.DDA.DispIdHost.addAsyncMethods(this,[a.GetSelectedDataAsync,a.GetSelectedTask,a.GetTask,a.GetTaskField,a.GetWSSUrl,a.GetSelectedResource,a.GetResourceField,a.GetProjectField,a.GetSelectedView,a.GetTaskByIndex,a.GetResourceByIndex,a.SetTaskField,a.SetResourceField,a.GetMaxTaskIndex,a.GetMaxResourceIndex,a.CreateTask]);OSF.DDA.DispIdHost.addEventSupport(this,new OSF.EventDispatch([Microsoft.Office.WebExtension.EventType.TaskSelectionChanged,Microsoft.Office.WebExtension.EventType.ResourceSelectionChanged,Microsoft.Office.WebExtension.EventType.ViewSelectionChanged]))};OSF.OUtil.extend(OSF.DDA.ProjectDocument,OSF.DDA.Document);OSF.DDA.TaskSelectionChangedEventArgs=function(a){OSF.OUtil.defineEnumerableProperties(this,{type:{value:Microsoft.Office.WebExtension.EventType.TaskSelectionChanged},document:{value:a}})};OSF.DDA.ResourceSelectionChangedEventArgs=function(a){OSF.OUtil.defineEnumerableProperties(this,{type:{value:Microsoft.Office.WebExtension.EventType.ResourceSelectionChanged},document:{value:a}})};OSF.DDA.ViewSelectionChangedEventArgs=function(a){OSF.OUtil.defineEnumerableProperties(this,{type:{value:Microsoft.Office.WebExtension.EventType.ViewSelectionChanged},document:{value:a}})};OSF.InitializationHelper.prototype.loadAppSpecificScriptAndCreateOM=function(a,b){OSF.DDA.ErrorCodeManager.initializeErrorMessages(Strings.OfficeOM);a.doc=new OSF.DDA.ProjectDocument(a,this._initializeSettings(false));b()};OSF.DDA.AsyncMethodNames.addNames({GetSelectedTask:"getSelectedTaskAsync",GetTask:"getTaskAsync",GetWSSUrl:"getWSSUrlAsync",GetTaskField:"getTaskFieldAsync",GetSelectedResource:"getSelectedResourceAsync",GetResourceField:"getResourceFieldAsync",GetProjectField:"getProjectFieldAsync",GetSelectedView:"getSelectedViewAsync",GetTaskByIndex:"getTaskByIndexAsync",GetResourceByIndex:"getResourceByIndexAsync",SetTaskField:"setTaskFieldAsync",SetResourceField:"setResourceFieldAsync",GetMaxTaskIndex:"getMaxTaskIndexAsync",GetMaxResourceIndex:"getMaxResourceIndexAsync",CreateTask:"createTaskAsync"});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetSelectedTask,onSucceeded:function(a){return a[Microsoft.Office.WebExtension.Parameters.TaskId]}});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetTaskByIndex,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.TaskIndex,types:["number"]}],supportedOptions:[],privateStateCallbacks:[],onSucceeded:function(a){return a[Microsoft.Office.WebExtension.Parameters.TaskId]}});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetTask,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.TaskId,types:["string"]}]});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetTaskField,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.TaskId,types:["string"]},{name:Microsoft.Office.WebExtension.Parameters.FieldId,types:["number"]}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.GetRawValue,value:{types:["boolean"],defaultValue:false}},{name:Microsoft.Office.WebExtension.Parameters.CustomFieldId,value:{types:["string"],calculate:function(){return ""}}}]});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetResourceField,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.ResourceId,types:["string"]},{name:Microsoft.Office.WebExtension.Parameters.FieldId,types:["number"]}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.GetRawValue,value:{types:["boolean"],defaultValue:false}},{name:Microsoft.Office.WebExtension.Parameters.CustomFieldId,value:{types:["string"],calculate:function(){return ""}}}]});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetProjectField,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.FieldId,types:["number"]}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.GetRawValue,value:{types:["boolean"],defaultValue:false}}]});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetSelectedResource,onSucceeded:function(a){return a[Microsoft.Office.WebExtension.Parameters.ResourceId]}});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetResourceByIndex,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.ResourceIndex,types:["number"]}],supportedOptions:[],privateStateCallbacks:[],onSucceeded:function(a){return a[Microsoft.Office.WebExtension.Parameters.ResourceId]}});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetWSSUrl});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetSelectedView});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.SetTaskField,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.TaskId,types:["string"]},{name:Microsoft.Office.WebExtension.Parameters.FieldId,types:["number"]},{name:Microsoft.Office.WebExtension.Parameters.FieldValue,types:["object","string","number","boolean"]}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.CustomFieldId,value:{types:["string"],calculate:function(){return ""}}}]});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.SetResourceField,requiredArguments:[{name:Microsoft.Office.WebExtension.Parameters.ResourceId,types:["string"]},{name:Microsoft.Office.WebExtension.Parameters.FieldId,types:["number"]},{name:Microsoft.Office.WebExtension.Parameters.FieldValue,types:["object","string","number","boolean"]}],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.CustomFieldId,value:{types:["string"],calculate:function(){return ""}}}]});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetMaxTaskIndex,onSucceeded:function(a){return a[Microsoft.Office.WebExtension.Parameters.TaskIndex]}});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.GetMaxResourceIndex,onSucceeded:function(a){return a[Microsoft.Office.WebExtension.Parameters.ResourceIndex]}});OSF.DDA.AsyncMethodCalls.define({method:OSF.DDA.AsyncMethodNames.CreateTask,requiredArguments:[],supportedOptions:[{name:Microsoft.Office.WebExtension.Parameters.TaskIndex,value:{types:["number"],defaultValue:0}},{name:Microsoft.Office.WebExtension.Parameters.ItemName,value:{types:["string"],calculate:function(){return ""}}}],privateStateCallbacks:[],onSucceeded:function(a){return a[Microsoft.Office.WebExtension.Parameters.TaskId]}});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetSelectedTaskMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.TaskId,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetTaskByIndexMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.TaskId,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}],toHost:[{name:Microsoft.Office.WebExtension.Parameters.TaskIndex,value:0}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetTaskMethod,fromHost:[{name:"taskName",value:0},{name:"wssTaskId",value:1},{name:"resourceNames",value:2}],toHost:[{name:Microsoft.Office.WebExtension.Parameters.TaskId,value:0}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetTaskFieldMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.FieldValue,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}],toHost:[{name:Microsoft.Office.WebExtension.Parameters.TaskId,value:0},{name:Microsoft.Office.WebExtension.Parameters.FieldId,value:1},{name:Microsoft.Office.WebExtension.Parameters.GetRawValue,value:2},{name:Microsoft.Office.WebExtension.Parameters.CustomFieldId,value:3}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetWSSUrlMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.ServerUrl,value:0},{name:Microsoft.Office.WebExtension.Parameters.ListName,value:1}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetSelectedResourceMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.ResourceId,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetResourceByIndexMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.ResourceId,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}],toHost:[{name:Microsoft.Office.WebExtension.Parameters.ResourceIndex,value:0}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetResourceFieldMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.FieldValue,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}],toHost:[{name:Microsoft.Office.WebExtension.Parameters.ResourceId,value:0},{name:Microsoft.Office.WebExtension.Parameters.FieldId,value:1},{name:Microsoft.Office.WebExtension.Parameters.GetRawValue,value:2},{name:Microsoft.Office.WebExtension.Parameters.CustomFieldId,value:3}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetProjectFieldMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.FieldValue,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}],toHost:[{name:Microsoft.Office.WebExtension.Parameters.FieldId,value:0},{name:Microsoft.Office.WebExtension.Parameters.GetRawValue,value:1}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetSelectedViewMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.ViewType,value:0},{name:Microsoft.Office.WebExtension.Parameters.ViewName,value:1}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidSetTaskFieldMethod,toHost:[{name:Microsoft.Office.WebExtension.Parameters.TaskId,value:0},{name:Microsoft.Office.WebExtension.Parameters.FieldId,value:1},{name:Microsoft.Office.WebExtension.Parameters.FieldValue,value:2},{name:Microsoft.Office.WebExtension.Parameters.CustomFieldId,value:3}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidSetResourceFieldMethod,toHost:[{name:Microsoft.Office.WebExtension.Parameters.ResourceId,value:0},{name:Microsoft.Office.WebExtension.Parameters.FieldId,value:1},{name:Microsoft.Office.WebExtension.Parameters.FieldValue,value:2},{name:Microsoft.Office.WebExtension.Parameters.CustomFieldId,value:3}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetMaxTaskIndexMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.TaskIndex,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidGetMaxResourceIndexMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.ResourceIndex,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.MethodDispId.dispidCreateTaskMethod,fromHost:[{name:Microsoft.Office.WebExtension.Parameters.TaskId,value:OSF.DDA.SafeArray.Delegate.ParameterMap.self}],toHost:[{name:Microsoft.Office.WebExtension.Parameters.TaskIndex,value:0},{name:Microsoft.Office.WebExtension.Parameters.ItemName,value:1}]});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.EventDispId.dispidTaskSelectionChangedEvent});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.EventDispId.dispidResourceSelectionChangedEvent});OSF.DDA.SafeArray.Delegate.ParameterMap.define({type:OSF.DDA.EventDispId.dispidViewSelectionChangedEvent})