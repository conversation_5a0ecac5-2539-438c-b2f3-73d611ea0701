if (window.Type && window.Type.registerNamespace) {
Type.registerNamespace('_u');} else {
if(typeof(window['_u']) == 'undefined') {
window['_u'] = new Object(); window['_u']. __namespace = true;
}

}

_u.ExtensibilityStrings=function _u_ExtensibilityStrings() {
}
if (_u.ExtensibilityStrings.registerClass) _u.ExtensibilityStrings.registerClass('_u.ExtensibilityStrings');
_u.ExtensibilityStrings.l_APICallFailedDueToItemChange_Text='The selected item has been changed.';
_u.ExtensibilityStrings.l_API_Not_Supported_By_ExtensionPoint_Error_Text='API not supported for extension point.';
_u.ExtensibilityStrings.l_API_Not_Supported_For_Shared_Folders_Error='API not supported for shared folders.';
_u.ExtensibilityStrings.l_ActionsDefinitionMultipleActionsError_Text='Only a single action is currently supported.';
_u.ExtensibilityStrings.l_ActionsDefinitionWrongNotificationMessageError_Text='Actions can\'t be defined for this notification message type.';
_u.ExtensibilityStrings.l_AttachmentDeleteGeneralFailure_Text='The attachment cannot be deleted from the item.';
_u.ExtensibilityStrings.l_AttachmentDeletedBeforeUploadCompletes_Text='The user removed the attachment before upload has completed.';
_u.ExtensibilityStrings.l_AttachmentErrorName_Text='Attachment Error';
_u.ExtensibilityStrings.l_AttachmentExceededSize_Text='The attachment cannot be added because it is too big.';
_u.ExtensibilityStrings.l_AttachmentItemIdTooLong_Text='One or more of the attachment IDs is too long.';
_u.ExtensibilityStrings.l_AttachmentNameTooLong_Text='One or more of the attachment names is too long.';
_u.ExtensibilityStrings.l_AttachmentNotSupported_Text='The attachment type is not supported.';
_u.ExtensibilityStrings.l_AttachmentUploadGeneralFailure_Text='The attachment cannot be added to the item.';
_u.ExtensibilityStrings.l_AttachmentUrlTooLong_Text='One or more of the attachment URLs is too long.';
_u.ExtensibilityStrings.l_Attachment_Download_Failed_Generic_Error='Downloading the attachment failed.';
_u.ExtensibilityStrings.l_Attachment_Resource_Not_Found='The attachment was not found.';
_u.ExtensibilityStrings.l_Attachment_Resource_UnAuthorizedAccess='Unauthorized access to the attachment.';
_u.ExtensibilityStrings.l_CallSaveAsyncBeforeToken_Text='The token can\'t be retrieved until the item is saved.';
_u.ExtensibilityStrings.l_CannotAddAttachmentBeforeUpgrade_Text='Attachments cannot be added while the full reply or forward is being retrieved from the server.';
_u.ExtensibilityStrings.l_CannotPersistPropertyInUnsavedDraftError_Text='Notifications can\'t be persisted in unsaved drafts. Save the item before you call this API.';
_u.ExtensibilityStrings.l_CursorPositionChanged_Text='The user changed the position of the cursor while the data was being inserted.';
_u.ExtensibilityStrings.l_DataWriteErrorName_Text='Data Write Error';
_u.ExtensibilityStrings.l_DeleteAttachmentDoesNotExist_Text='The attachment cannot be deleted because attachment with the attachment index cannot be found.';
_u.ExtensibilityStrings.l_DisplayNameTooLong_Text='One or more of the display names provided is too long.';
_u.ExtensibilityStrings.l_DisplayReplyFormHtmlBodyRequired_Text='The \'htmlBody\' is required.';
_u.ExtensibilityStrings.l_DuplicateNotificationKey_Text='A notification with the provided key already exists.';
_u.ExtensibilityStrings.l_Duplicate_Category_Error_Text='One of the categories provided is already in the master category list.';
_u.ExtensibilityStrings.l_ElevatedPermissionNeededForMethod_Text='Elevated permission is required to call the method: \'{0}\'.';
_u.ExtensibilityStrings.l_ElevatedPermissionNeeded_Text='Elevated permission is required to access protected members of the JavaScript API for Office.';
_u.ExtensibilityStrings.l_EmailAddressTooLong_Text='One or more of the email addresses provided is too long.';
_u.ExtensibilityStrings.l_EwsRequestOversized_Text='The request exceeds the 1 MB size limit. Please modify your EWS request.';
_u.ExtensibilityStrings.l_ExceededMaxNumberOfAttachments_Text='Attachments cannot be added because the message already has the maximum number of attachments';
_u.ExtensibilityStrings.l_HtmlSanitizationFailure_Text='The HTML santization has failed.';
_u.ExtensibilityStrings.l_Insufficient_Item_Permissions_Text='The user doesn\'t have the permissions required to perform this operation.';
_u.ExtensibilityStrings.l_InternalFormatError_Text='There was an internal format error.';
_u.ExtensibilityStrings.l_InternalProtocolError_Text='Internal protocol error: \'{0}\'.';
_u.ExtensibilityStrings.l_Internal_Server_Error_Text='The Exchange server returned an error. Please look at the diagnostics object for more information.';
_u.ExtensibilityStrings.l_Internet_Not_Connected_Error_Text='The user is no longer connected to the network. Please check your network connection and try again.';
_u.ExtensibilityStrings.l_InvalidActionType_Text='The value of the parameter \'actionType\' is invalid. The accepted value is \"showTaskPane\".';
_u.ExtensibilityStrings.l_InvalidAttachmentId_Text='The attachment ID was invalid.';
_u.ExtensibilityStrings.l_InvalidAttachmentPath_Text='The attachment path was invalid.';
_u.ExtensibilityStrings.l_InvalidCommandIdError_Text='The value of the parameter \'commandId\' is invalid.';
_u.ExtensibilityStrings.l_InvalidDate_Text='The input doesn\'t resolve to a valid date.';
_u.ExtensibilityStrings.l_InvalidEndTime_Text='The end time cannot be before the start time.';
_u.ExtensibilityStrings.l_InvalidEventDates_Text='The end date occurs before the start date.';
_u.ExtensibilityStrings.l_InvalidParameterValueError_Text='The value of the \'{0}\' parameter is invalid.';
_u.ExtensibilityStrings.l_InvalidSelection_Text='The selection is invalid.';
_u.ExtensibilityStrings.l_InvalidTime_Text='The input doesn\'t resolve to a valid time.';
_u.ExtensibilityStrings.l_Invalid_Category_Error_Text='Invalid categories were provided.';
_u.ExtensibilityStrings.l_ItemNotFound_Text='The item does not exist or has not been created.';
_u.ExtensibilityStrings.l_Item_Not_Saved_Error_Text='The id can\'t be retrieved until the item is saved.';
_u.ExtensibilityStrings.l_KeyNotFound_Text='The specified key was not found.';
_u.ExtensibilityStrings.l_MessageInDifferentStoreError_Text='The EWS ID can\'t be retrieved because the message is saved in a different store.';
_u.ExtensibilityStrings.l_Missing_Extended_Permissions_For_API='Extended Permission missing for the API call.';
_u.ExtensibilityStrings.l_NoValidRecipientsProvided_Text='No valid recipients were provided.';
_u.ExtensibilityStrings.l_NotificationKeyNotFound_Text='There are no notifications with the provided key.';
_u.ExtensibilityStrings.l_NullOrEmptyParameterError_Text='The parameter \'{0}\' is mandatory and it can\'t be null or empty.';
_u.ExtensibilityStrings.l_NumberOfNotificationsExceeded_Text='The notification couldn\'t be added because the notification limit has been reached.';
_u.ExtensibilityStrings.l_NumberOfRecipientsExceeded_Text='The total number of recipients in the field can\'t exceed {0}.';
_u.ExtensibilityStrings.l_OffsetNotfound_Text='An offset for this time stamp couldn\'t be found.';
_u.ExtensibilityStrings.l_Olk_Http_Error_Text='The request has failed. Please look at the diagnostics object for the HTTP error code.';
_u.ExtensibilityStrings.l_OnlineMeetingsUserDenied_Text='User denied.';
_u.ExtensibilityStrings.l_ParameterValueTooLongError_Text='The value of the \'{0}\' parameter is too long. The maximum number of characters is \'{1}\'.';
_u.ExtensibilityStrings.l_ParametersNotAsExpected_Text='The given parameters do not match the expected format.';
_u.ExtensibilityStrings.l_PersistedNotificationArrayReadError_Text='The API call you made failed because the persisted notifications couldn\'t be retrieved.';
_u.ExtensibilityStrings.l_PersistedNotificationArraySaveError_Text='The API call you made failed because notifications couldn\'t be persisted.';
_u.ExtensibilityStrings.l_RecurrenceErrorMaxOccurrences_Text='The recurring series exceeds the maximum limit of 999 occurrences.';
_u.ExtensibilityStrings.l_RecurrenceErrorZeroOccurrences_Text='The recurring series has no occurrences in the specified date range.';
_u.ExtensibilityStrings.l_RecurrenceInvalidTimeZone_Text='The specified time zone is not supported.';
_u.ExtensibilityStrings.l_RecurrenceUnsupportedAlternateCalendar_Text='The recurrence pattern was set by the user using an alternate calendar that is not supported.';
_u.ExtensibilityStrings.l_Recurrence_Error_Instance_SetAsync_Text='A recurrence pattern can\'t be set for a single occurrence in a series.';
_u.ExtensibilityStrings.l_Recurrence_Error_Properties_Invalid_Text='The recurrence pattern isn\'t valid. Please check that the specified recurrence properties align with the recurrence type.';
_u.ExtensibilityStrings.l_RoamingSettingsExceededSize_Text='Invalid parameter size. Roaming settings can\'t exceed the 32 KB size limit.';
_u.ExtensibilityStrings.l_SaveError_Text='Connection error occurred while trying to save the item on the server.';
_u.ExtensibilityStrings.l_SessionDataObjectMaxLengthExceeded_Text='The \'sessionData\' object surpassed its maximum length of \'{0}\' characters.';
_u.ExtensibilityStrings.l_TokenAccessDeniedWithoutItemContext_Text='A REST token is only available with ReadWriteMailbox permission when there is no item context.';

