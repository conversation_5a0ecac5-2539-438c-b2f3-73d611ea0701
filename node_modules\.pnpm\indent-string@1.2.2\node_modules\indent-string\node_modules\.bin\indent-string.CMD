@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\ExcelAiRate\node_modules\.pnpm\indent-string@1.2.2\node_modules\indent-string\node_modules;C:\Users\<USER>\ExcelAiRate\node_modules\.pnpm\indent-string@1.2.2\node_modules;C:\Users\<USER>\ExcelAiRate\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\ExcelAiRate\node_modules\.pnpm\indent-string@1.2.2\node_modules\indent-string\node_modules;C:\Users\<USER>\ExcelAiRate\node_modules\.pnpm\indent-string@1.2.2\node_modules;C:\Users\<USER>\ExcelAiRate\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\cli.js" %*
)
