#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/uuid@3.4.0/node_modules/uuid/bin/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/uuid@3.4.0/node_modules/uuid/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/uuid@3.4.0/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/uuid@3.4.0/node_modules/uuid/bin/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/uuid@3.4.0/node_modules/uuid/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/uuid@3.4.0/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/uuid" "$@"
else
  exec node  "$basedir/../../bin/uuid" "$@"
fi
