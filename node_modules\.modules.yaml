hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/runtime-corejs3@7.27.0':
    '@babel/runtime-corejs3': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.30.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': public
  '@eslint/core@0.14.0':
    '@eslint/core': public
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': public
  '@eslint/js@9.30.0':
    '@eslint/js': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.3.3':
    '@eslint/plugin-kit': public
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.34.1':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.1':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.1':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.1':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.1':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.1':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.1':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.1':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.1':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.34.1':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.1':
    '@img/sharp-win32-x64': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@next/env@15.3.5':
    '@next/env': private
  '@next/eslint-plugin-next@15.3.0':
    '@next/eslint-plugin-next': public
  '@next/swc-darwin-arm64@15.3.5':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.3.5':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.3.5':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.3.5':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.3.5':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.3.5':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.3.5':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.3.5':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@playwright/test@1.53.2':
    '@playwright/test': private
  '@rollup/rollup-android-arm-eabi@4.44.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.2':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.12.0':
    '@rushstack/eslint-patch': public
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.11':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.11':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@tailwindcss/postcss@4.1.11':
    '@tailwindcss/postcss': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@turbo/gen@2.5.0(@types/node@22.15.3)(typescript@5.8.2)':
    '@turbo/gen': private
  '@turbo/workspaces@2.5.0':
    '@turbo/workspaces': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/glob@7.2.0':
    '@types/glob': private
  '@types/inquirer@6.5.0':
    '@types/inquirer': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/minimatch@5.1.2':
    '@types/minimatch': private
  '@types/node@20.19.4':
    '@types/node': private
  '@types/office-js@1.0.514':
    '@types/office-js': private
  '@types/react-dom@19.1.1(@types/react@19.1.0)':
    '@types/react-dom': private
  '@types/react@19.1.0':
    '@types/react': private
  '@types/through@0.0.33':
    '@types/through': private
  '@types/tinycolor2@1.4.6':
    '@types/tinycolor2': private
  '@typescript-eslint/eslint-plugin@8.35.0(@typescript-eslint/parser@8.35.0(eslint@9.30.0(jiti@2.4.2))(typescript@5.8.2))(eslint@9.30.0(jiti@2.4.2))(typescript@5.8.2)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.35.0(eslint@9.30.0(jiti@2.4.2))(typescript@5.8.2)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/project-service@8.35.0(typescript@5.8.2)':
    '@typescript-eslint/project-service': public
  '@typescript-eslint/scope-manager@8.35.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/tsconfig-utils@8.35.0(typescript@5.8.2)':
    '@typescript-eslint/tsconfig-utils': public
  '@typescript-eslint/type-utils@8.35.0(eslint@9.30.0(jiti@2.4.2))(typescript@5.8.2)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.35.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.35.0(typescript@5.8.2)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.35.0(eslint@9.30.0(jiti@2.4.2))(typescript@5.8.2)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.35.0':
    '@typescript-eslint/visitor-keys': public
  '@unrs/resolver-binding-android-arm-eabi@1.10.1':
    '@unrs/resolver-binding-android-arm-eabi': private
  '@unrs/resolver-binding-android-arm64@1.10.1':
    '@unrs/resolver-binding-android-arm64': private
  '@unrs/resolver-binding-darwin-arm64@1.10.1':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.10.1':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.10.1':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.10.1':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.10.1':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.10.1':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.10.1':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.10.1':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.10.1':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.10.1':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.10.1':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.10.1':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.10.1':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.10.1':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.10.1':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.10.1':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.10.1':
    '@unrs/resolver-binding-win32-x64-msvc': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  apps/excel-addin:
    excel-addin: private
  apps/web:
    web: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.9:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  asn1@0.1.11:
    asn1: private
  assert-plus@0.1.5:
    assert-plus: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  ast-types@0.13.4:
    ast-types: private
  async-function@1.0.0:
    async-function: private
  async@0.9.2:
    async: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  aws-sign2@0.5.0:
    aws-sign2: private
  aws4@1.13.2:
    aws4: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  base64url@1.0.6:
    base64url: private
  basic-ftp@5.0.5:
    basic-ftp: private
  bcrypt-pbkdf@1.0.2:
    bcrypt-pbkdf: private
  bl@4.1.0:
    bl: private
  boom@0.4.2:
    boom: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer@5.7.1:
    buffer: private
  busboy@1.6.0:
    busboy: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@3.0.0:
    camel-case: private
  camelcase-keys@1.0.0:
    camelcase-keys: private
  camelcase@1.2.1:
    camelcase: private
  caniuse-lite@1.0.30001713:
    caniuse-lite: private
  caseless@0.8.0:
    caseless: private
  chalk@4.1.2:
    chalk: private
  change-case@3.1.0:
    change-case: private
  chardet@0.7.0:
    chardet: private
  chownr@3.0.0:
    chownr: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-width@3.0.0:
    cli-width: private
  client-only@0.0.1:
    client-only: private
  clone@1.0.4:
    clone: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  combined-stream@0.0.7:
    combined-stream: private
  commander@10.0.1:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@1.4.11:
    concat-stream: private
  constant-case@2.0.0:
    constant-case: private
  core-js-pure@3.41.0:
    core-js-pure: private
  core-util-is@1.0.3:
    core-util-is: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cryptiles@0.2.2:
    cryptiles: private
  csstype@3.1.3:
    csstype: private
  ctype@0.5.3:
    ctype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  dashdash@1.14.1:
    dashdash: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  defaults@1.0.4:
    defaults: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  degenerator@5.0.1:
    degenerator: private
  del@5.1.0:
    del: private
  delayed-stream@0.0.5:
    delayed-stream: private
  detect-libc@2.0.4:
    detect-libc: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@2.1.0:
    doctrine: private
  dot-case@2.1.1:
    dot-case: private
  dotenv@16.0.3:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ecc-jsbn@0.1.2:
    ecc-jsbn: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  emoji-regex@9.2.2:
    emoji-regex: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.25.5:
    esbuild: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-config-next@15.3.5(eslint@9.30.0(jiti@2.4.2))(typescript@5.8.2):
    eslint-config-next: public
  eslint-config-prettier@10.1.1(eslint@9.30.0(jiti@2.4.2)):
    eslint-config-prettier: public
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0)(eslint@9.30.0(jiti@2.4.2)):
    eslint-import-resolver-typescript: public
  eslint-module-utils@2.12.1(@typescript-eslint/parser@8.35.0(eslint@9.30.0(jiti@2.4.2))(typescript@5.8.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0)(eslint@9.30.0(jiti@2.4.2)))(eslint@9.30.0(jiti@2.4.2)):
    eslint-module-utils: public
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@8.35.0(eslint@9.30.0(jiti@2.4.2))(typescript@5.8.2))(eslint-import-resolver-typescript@3.10.1)(eslint@9.30.0(jiti@2.4.2)):
    eslint-plugin-import: public
  eslint-plugin-jsx-a11y@6.10.2(eslint@9.30.0(jiti@2.4.2)):
    eslint-plugin-jsx-a11y: public
  eslint-plugin-only-warn@1.1.0:
    eslint-plugin-only-warn: public
  eslint-plugin-react-hooks@5.2.0(eslint@9.30.0(jiti@2.4.2)):
    eslint-plugin-react-hooks: public
  eslint-plugin-react@7.37.5(eslint@9.30.0(jiti@2.4.2)):
    eslint-plugin-react: public
  eslint-plugin-turbo@2.5.0(eslint@9.30.0(jiti@2.4.2))(turbo@2.5.4):
    eslint-plugin-turbo: public
  eslint-scope@8.4.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: public
  eslint@9.30.0(jiti@2.4.2):
    eslint: public
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  execa@5.1.1:
    execa: private
  extend@3.0.2:
    extend: private
  external-editor@3.1.0:
    external-editor: private
  extsprintf@1.3.0:
    extsprintf: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.1:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  figures@3.2.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  forever-agent@0.5.2:
    forever-agent: private
  form-data@0.2.0:
    form-data: private
  fs-extra@10.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gapitoken@0.1.5:
    gapitoken: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stdin@4.0.1:
    get-stdin: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  get-uri@6.0.4:
    get-uri: private
  getpass@0.1.7:
    getpass: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@16.2.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@10.0.2:
    globby: private
  googleapis@1.1.5:
    googleapis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  gradient-string@2.0.2:
    gradient-string: private
  graphemer@1.4.0:
    graphemer: private
  handlebars@4.7.8:
    handlebars: private
  har-schema@2.0.0:
    har-schema: private
  har-validator@5.1.5:
    har-validator: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hawk@1.1.1:
    hawk: private
  header-case@1.0.1:
    header-case: private
  hoek@0.9.1:
    hoek: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  http-signature@0.10.1:
    http-signature: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inquirer@8.2.6:
    inquirer: private
  internal-slot@1.1.0:
    internal-slot: private
  ip-address@9.0.5:
    ip-address: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-finite@1.1.0:
    is-finite: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-interactive@1.0.0:
    is-interactive: private
  is-lower-case@1.1.3:
    is-lower-case: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-cwd@2.2.0:
    is-path-cwd: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-upper-case@1.1.2:
    is-upper-case: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@0.0.1:
    isarray: private
  isbinaryfile@4.0.10:
    isbinaryfile: private
  isexe@2.0.0:
    isexe: private
  isstream@0.1.2:
    isstream: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jiti@2.4.2:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json5@1.0.2:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsprim@1.4.2:
    jsprim: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  jszip@2.7.0:
    jszip: private
  jwa@1.0.2:
    jwa: private
  jws@3.0.0:
    jws: private
  keyv@4.5.4:
    keyv: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  linq@3.2.4:
    linq: private
  locate-path@6.0.0:
    locate-path: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  log-symbols@3.0.0:
    log-symbols: private
  loose-envify@1.4.0:
    loose-envify: private
  lower-case-first@1.0.2:
    lower-case-first: private
  lower-case@1.1.4:
    lower-case: private
  lru-cache@7.18.3:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  make-error@1.3.6:
    make-error: private
  map-obj@1.0.1:
    map-obj: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  meow@2.0.0:
    meow: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.12.0:
    mime-db: private
  mime-types@1.0.2:
    mime-types: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@0.5.6:
    mkdirp: private
  ms@2.1.3:
    ms: private
  mute-stream@0.0.8:
    mute-stream: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.3.0:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  neo-async@2.6.2:
    neo-async: private
  netmask@2.0.2:
    netmask: private
  next@15.3.5(@playwright/test@1.53.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    next: private
  no-case@2.3.2:
    no-case: private
  node-plop@0.26.3:
    node-plop: private
  node-uuid@1.4.8:
    node-uuid: private
  npm-run-path@4.0.1:
    npm-run-path: private
  oauth-sign@0.5.0:
    oauth-sign: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  office-js@0.1.0:
    office-js: private
  office365-js@https://codeload.github.com/happy-tanuki/office365-js/tar.gz/4935109a93f86eda73ce9d72fcfb34c99e88193e:
    office365-js: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  openxml-js@https://codeload.github.com/happy-tanuki/openxml-js/tar.gz/b6e38cfde736264da69ad34c624c94ae3c7f8be8:
    openxml-js: private
  optionator@0.9.4:
    optionator: private
  ora@4.1.1:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@3.0.0:
    p-map: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  packages/eslint-config:
    '@repo/eslint-config': public
  packages/typescript-config:
    '@repo/typescript-config': private
  packages/ui:
    '@repo/ui': private
  pako@1.0.11:
    pako: private
  param-case@2.1.1:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  pascal-case@2.0.1:
    pascal-case: private
  path-case@2.1.1:
    path-case: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-type@4.0.0:
    path-type: private
  performance-now@2.1.0:
    performance-now: private
  picocolors@1.0.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  playwright-core@1.53.2:
    playwright-core: private
  playwright@1.53.2:
    playwright: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prop-types@15.8.1:
    prop-types: private
  proxy-agent@6.5.0:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  punycode@2.3.1:
    punycode: private
  qs@2.3.3:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  rc@1.2.8:
    rc: private
  react-dom@19.1.0(react@19.1.0):
    react-dom: private
  react-is@16.13.1:
    react-is: private
  react@19.1.0:
    react: private
  readable-stream@1.0.34:
    readable-stream: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  registry-auth-token@3.3.2:
    registry-auth-token: private
  registry-url@3.1.0:
    registry-url: private
  repeating@1.1.3:
    repeating: private
  request@2.51.0:
    request: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@2.0.0-next.5:
    resolve: private
  restore-cursor@3.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.44.2:
    rollup: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  semver@6.3.1:
    semver: private
  sentence-case@2.1.1:
    sentence-case: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  sharp@0.34.1:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  slash@3.0.0:
    slash: private
  smart-buffer@4.2.0:
    smart-buffer: private
  snake-case@2.1.0:
    snake-case: private
  sntp@0.2.4:
    sntp: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.4:
    socks: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map@0.6.1:
    source-map: private
  sprintf-js@1.1.3:
    sprintf-js: private
  sshpk@1.18.0:
    sshpk: private
  stable-hash@0.0.5:
    stable-hash: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  streamsearch@1.1.0:
    streamsearch: private
  string-template@0.2.1:
    string-template: private
  string-width@4.2.3:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@0.10.31:
    string_decoder: private
  stringstream@0.0.6:
    stringstream: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  styled-jsx@5.1.6(react@19.1.0):
    styled-jsx: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swap-case@1.1.2:
    swap-case: private
  tailwindcss@4.1.11:
    tailwindcss: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  through@2.3.8:
    through: private
  tinycolor2@1.6.0:
    tinycolor2: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tinygradient@1.1.5:
    tinygradient: private
  title-case@2.1.1:
    title-case: private
  tldts-core@6.1.86:
    tldts-core: private
  tldts@6.1.86:
    tldts: private
  tmp@0.0.33:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tough-cookie@5.1.2:
    tough-cookie: private
  ts-api-utils@2.1.0(typescript@5.8.2):
    ts-api-utils: private
  ts-node@10.9.2(@types/node@22.15.3)(typescript@5.8.2):
    ts-node: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.4.3:
    tunnel-agent: private
  turbo-darwin-64@2.5.4:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.4:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.4:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.4:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.4:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.4:
    turbo-windows-arm64: private
  tweetnacl@0.14.5:
    tweetnacl: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.21.3:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typedarray@0.0.7:
    typedarray: private
  typescript-eslint@8.35.0(eslint@9.30.0(jiti@2.4.2))(typescript@5.8.2):
    typescript-eslint: public
  uglify-js@3.19.3:
    uglify-js: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  universalify@2.0.1:
    universalify: private
  unrs-resolver@1.10.1:
    unrs-resolver: private
  update-check@1.5.4:
    update-check: private
  upper-case-first@1.1.2:
    upper-case-first: private
  upper-case@1.1.3:
    upper-case: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@3.4.0:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  validate-npm-package-name@5.0.1:
    validate-npm-package-name: private
  verror@1.10.0:
    verror: private
  vite@7.0.2(@types/node@22.15.3)(jiti@2.4.2)(lightningcss@1.30.1):
    vite: private
  wcwidth@1.0.1:
    wcwidth: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wordwrap@1.0.0:
    wordwrap: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  xmldom@0.1.31:
    xmldom: private
  xmlhttprequest@https://codeload.github.com/happy-tanuki/node-XMLHttpRequest/tar.gz/4e0f40a6f2be06896e3a2e18b786e2ed4a0c9cef:
    xmlhttprequest: private
  yallist@5.0.0:
    yallist: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.0.0
pendingBuilds: []
prunedAt: Sat, 05 Jul 2025 02:33:11 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.1'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@img/sharp-darwin-arm64@0.34.1'
  - '@img/sharp-darwin-x64@0.34.1'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.34.1'
  - '@img/sharp-linux-arm@0.34.1'
  - '@img/sharp-linux-s390x@0.34.1'
  - '@img/sharp-linux-x64@0.34.1'
  - '@img/sharp-linuxmusl-arm64@0.34.1'
  - '@img/sharp-linuxmusl-x64@0.34.1'
  - '@img/sharp-wasm32@0.34.1'
  - '@img/sharp-win32-ia32@0.34.1'
  - '@napi-rs/wasm-runtime@0.2.11'
  - '@next/swc-darwin-arm64@15.3.5'
  - '@next/swc-darwin-x64@15.3.5'
  - '@next/swc-linux-arm64-gnu@15.3.5'
  - '@next/swc-linux-arm64-musl@15.3.5'
  - '@next/swc-linux-x64-gnu@15.3.5'
  - '@next/swc-linux-x64-musl@15.3.5'
  - '@next/swc-win32-arm64-msvc@15.3.5'
  - '@rollup/rollup-android-arm-eabi@4.44.2'
  - '@rollup/rollup-android-arm64@4.44.2'
  - '@rollup/rollup-darwin-arm64@4.44.2'
  - '@rollup/rollup-darwin-x64@4.44.2'
  - '@rollup/rollup-freebsd-arm64@4.44.2'
  - '@rollup/rollup-freebsd-x64@4.44.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.2'
  - '@rollup/rollup-linux-arm64-gnu@4.44.2'
  - '@rollup/rollup-linux-arm64-musl@4.44.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-musl@4.44.2'
  - '@rollup/rollup-linux-s390x-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-musl@4.44.2'
  - '@rollup/rollup-win32-arm64-msvc@4.44.2'
  - '@rollup/rollup-win32-ia32-msvc@4.44.2'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.10.1'
  - '@unrs/resolver-binding-android-arm64@1.10.1'
  - '@unrs/resolver-binding-darwin-arm64@1.10.1'
  - '@unrs/resolver-binding-darwin-x64@1.10.1'
  - '@unrs/resolver-binding-freebsd-x64@1.10.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.10.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.10.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.10.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.10.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.10.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.10.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.10.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.10.1'
  - '@unrs/resolver-binding-linux-x64-gnu@1.10.1'
  - '@unrs/resolver-binding-linux-x64-musl@1.10.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.10.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.10.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.10.1'
  - fsevents@2.3.2
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - turbo-darwin-64@2.5.4
  - turbo-darwin-arm64@2.5.4
  - turbo-linux-64@2.5.4
  - turbo-linux-arm64@2.5.4
  - turbo-windows-arm64@2.5.4
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\ExcelAiRate\node_modules\.pnpm
