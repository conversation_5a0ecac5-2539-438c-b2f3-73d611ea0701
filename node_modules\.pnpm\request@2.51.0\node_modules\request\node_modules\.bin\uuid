#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node-uuid@1.4.8/node_modules/node-uuid/bin/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node-uuid@1.4.8/node_modules/node-uuid/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node-uuid@1.4.8/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node-uuid@1.4.8/node_modules/node-uuid/bin/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node-uuid@1.4.8/node_modules/node-uuid/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node-uuid@1.4.8/node_modules:/mnt/c/Users/<USER>/ExcelAiRate/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../node-uuid@1.4.8/node_modules/node-uuid/bin/uuid" "$@"
else
  exec node  "$basedir/../../../../../node-uuid@1.4.8/node_modules/node-uuid/bin/uuid" "$@"
fi
