## linq
This is a javascript implementation of the .NET [LINQ](https://msdn.microsoft.com/en-us/library/bb308959.aspx) library.

It contains all the original .NET methods plus a few additions.

### Installation

With npm:

    npm install linq

CDN availability:

|    CDN     | URL                                        |
|-----------:|:-------------------------------------------|
|    `unpkg` | <https://unpkg.com/linq/>                  |
| `jsDelivr` | <https://jsdelivr.com/package/npm/linq>    |
|    `packd` | <https://bundle.run/linq@latest?name=linq> |

### Examples
```js
// C# LINQ - delegate
Enumerable.Range(1, 10)
    .Where(delegate(int i) { return i % 3 == 0; })
    .Select(delegate(int i) { return i * 10; });

// linq.js - anonymous function
Enumerable.range(1, 10)
    .where(function(i) { return i % 3 == 0; })
    .select(function(i) { return i * 10; });
```

```js
// C# LINQ - lambda
Enumerable.Range(1, 10).Where(i => i % 3 == 0).Select(i => i * 10);

// linq.js - arrow function
Enumerable.range(1, 10).where(i => i % 3 == 0).select(i => i * 10);
```

```js
// C# LINQ - anonymous type
array.Select((val, i) => new { Value = val, Index = i });

// linq.js - object literal
Enumerable.from(array).select((val, i) => ({ value: val, index: i}));
```

See [sample/tutorial.js](https://github.com/mihaifm/linq/blob/master/sample/tutorial.js) for more examples.

### People

[Yoshifumi Kawai](https://github.com/neuecc) developed the [original version](http://linqjs.codeplex.com/) of this library, currently no longer maintained.

### License

[MIT License](https://github.com/mihaifm/linq/blob/master/LICENSE)
