﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Load, Modify, and Save using HTML5 (alternate UI)</title>
    <style>
        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <form>
        <div>
            <input id="fileInput" type="file" onchange="processFiles(this.files)" />
        </div>
        <div style="margin-top: 1em; margin-bottom: 1em;">
            <input id="btnOpen" type="button" onclick="openDocument()" value="Open" style="height: 30px; width: 100px; background-color: rgb(212,208,200); border: thin solid black;" />
        </div>
        <div style="margin-top: 1em; margin-bottom: 1em;">
            <input id="btnModify" type="button" onclick="modifyDocument()" value="Modify" style="height: 30px; width: 100px; background-color: rgb(212,208,200); border: thin solid black;" />
        </div>
        <div style="margin-top: 1em; margin-bottom: 1em;">
            <input id="btnSave" type="button" onclick="saveDocument()" value="Save" style="height: 30px; width: 100px; background-color: rgb(212,208,200); border: thin solid black;"  />
        </div>
        <p>If you are targeting down-level browsers that do not support HTML5, see <b>examp01-load-save-via-flash.html</b>.</p>
    </form>
    <pre>
<script type="text/javascript" src="linq.js"></script>
<script type="text/javascript" src="ltxml.js"></script>
<script type="text/javascript" src="ltxml-extensions.js"></script>
<script type="text/javascript" src="jszip.js"></script>
<script type="text/javascript" src="jszip-load.js"></script>
<script type="text/javascript" src="jszip-inflate.js"></script>
<script type="text/javascript" src="jszip-deflate.js"></script>
<script type="text/javascript" src="FileSaver.js"></script>
<script type="text/javascript" src="openxml.js"></script>

<script type="text/javascript">
    /***************************************************************************
    
    Copyright (c) Microsoft Corporation 2013.
    
    This code is licensed using the Microsoft Public License (Ms-PL).  You can find the text of the license here:
    
    http://www.microsoft.com/resources/sharedsource/licensingbasics/publiclicense.mspx
    
    Published at http://OpenXmlDeveloper.org
    Resource Center and Documentation: http://openxmldeveloper.org/wiki/w/wiki/open-xml-sdk-for-javascript.aspx
    
    Developer: Eric White
    Blog: http://www.ericwhite.com
    Twitter: @EricWhiteDev
	Email: <EMAIL>
    
    ***************************************************************************/

    var openDocument = null;
    var processFiles = null;
    var modifyDocument = null;
    var saveDocument = null;

    (function (root) {  // root = global
        "use strict";

        var XAttribute = Ltxml.XAttribute;
        var XCData = Ltxml.XCData;
        var XComment = Ltxml.XComment;
        var XContainer = Ltxml.XContainer;
        var XDeclaration = Ltxml.XDeclaration;
        var XDocument = Ltxml.XDocument;
        var XElement = Ltxml.XElement;
        var XName = Ltxml.XName;
        var XNamespace = Ltxml.XNamespace;
        var XNode = Ltxml.XNode;
        var XObject = Ltxml.XObject;
        var XProcessingInstruction = Ltxml.XProcessingInstruction;
        var XText = Ltxml.XText;
        var XEntity = Ltxml.XEntity;
        var cast = Ltxml.cast;
        var castInt = Ltxml.castInt;

        var W = openXml.W;
        var NN = openXml.NoNamespace;
        var wNs = openXml.wNs;

        root.processFiles = function (files) {
            var file = files[0];
            var reader = new FileReader();
            reader.onload = function (e) {
                // when this event is invoked, the document has been loaded
                openedFileName = file.name;
                openedFileData = e.target.result;
            };
            reader.readAsArrayBuffer(file);
        }

        var openedFileName = null;
        var openedFileData = null;

        root.modifyDocument = function () {
            if (openedFileName === null || openedFileData === null) {
                alert("You must open a file first.");
            }
            else {
                /******************************************************************************/
                // open the document
                openXml.util.bucketTimer.init();
                openXml.util.bucketTimer.bucket("OpenAndSave");
                var doc = new openXml.OpenXmlPackage(openedFileData);

                // add a paragraph to the beginning of the document.
                var body = doc.mainDocumentPart().getXDocument().root.element(W.body);
                body.addFirst(
                    new XElement(W.p,
                        new XElement(W.r,
                            new XElement(W.t, "New first paragraph."))));

                // serialize it to a blob
                openedFileData = doc.saveToBlob();

                openXml.util.bucketTimer.bucket("Done");
                var s = openXml.util.bucketTimer["OpenAndSave"];
                alert("Document modified.  Elapsed time: " + (s.time / 1000).toString() + " seconds");
                /******************************************************************************/
            }
        }

        root.openDocument = function () {
            var fileInput = document.getElementById('fileInput');
            fileInput.click();
        }

        root.saveDocument = function () {
            if (openedFileName === null || openedFileData === null) {
                alert("You must open a document first.");
            }
            else {
                saveAs(openedFileData, openedFileName);
            }
        }

    }(this));

</script>
</pre>
</body>
</html>
