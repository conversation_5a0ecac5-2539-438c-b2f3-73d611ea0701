﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Shows opening a workbook / worksheet and making a mod to the upper left cell.</title>
</head>
<body>
<form>
    <div style="margin-top: 1em; margin-bottom: 1em;">
        <input id="btnSave" type="button" onclick="saveDocument()" value="Save" style="height: 30px; width: 100px; background-color: rgb(212,208,200); border: thin solid black;"  />
    </div>
    <p>If you are targeting down-level browsers that do not support HTML5, see <b>examp01-load-save-via-flash.html</b>.</p>
</form>
<pre>
<script type="text/javascript" src="linq.js"></script>
<script type="text/javascript" src="ltxml.js"></script>
<script type="text/javascript" src="ltxml-extensions.js"></script>
<script type="text/javascript" src="jszip.js"></script>
<script type="text/javascript" src="jszip-load.js"></script>
<script type="text/javascript" src="jszip-inflate.js"></script>
<script type="text/javascript" src="jszip-deflate.js"></script>
<script type="text/javascript" src="FileSaver.js"></script>
<script type="text/javascript" src="openxml.js"></script>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript">
    /***************************************************************************
    
    Copyright (c) Microsoft Corporation 2013.
    
    This code is licensed using the Microsoft Public License (Ms-PL).  You can find the text of the license here:
    
    http://www.microsoft.com/resources/sharedsource/licensingbasics/publiclicense.mspx
    
    Published at http://OpenXmlDeveloper.org
    Resource Center and Documentation: http://openxmldeveloper.org/wiki/w/wiki/open-xml-sdk-for-javascript.aspx
    
    Developer: Eric White
    Blog: http://www.ericwhite.com
    Twitter: @EricWhiteDev
	Email: <EMAIL>
    
    ***************************************************************************/

    var saveSpreadsheet;
    var spreadsheetToSave;

    (function (root) {  // root = global
        "use strict";

        var XAttribute = Ltxml.XAttribute;
        var XCData = Ltxml.XCData;
        var XComment = Ltxml.XComment;
        var XContainer = Ltxml.XContainer;
        var XDeclaration = Ltxml.XDeclaration;
        var XDocument = Ltxml.XDocument;
        var XElement = Ltxml.XElement;
        var XName = Ltxml.XName;
        var XNamespace = Ltxml.XNamespace;
        var XNode = Ltxml.XNode;
        var XObject = Ltxml.XObject;
        var XProcessingInstruction = Ltxml.XProcessingInstruction;
        var XText = Ltxml.XText;
        var XEntity = Ltxml.XEntity;
        var cast = Ltxml.cast;
        var castInt = Ltxml.castInt;

        var S = openXml.S;
        var R = openXml.R;

        var templateSpreadsheet = "UEsDBBQABgAIAAAAIQD21qXvWgEAABgFAAATAAgCW0NvbnRlbnRfVHlwZXNdLnhtbCCiBAIooAAC" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADM" +
"lMtuwyAQRfeV+g+IbWWTpFJVVXGy6GPZZpF+ADVjGwUDYkia/H3H5LGo3FRRIrUbI8PMPRcYZjxd" +
"t4atIKB2tuDDfMAZ2NIpbeuCv89fsnvOMEqrpHEWCr4B5NPJ9dV4vvGAjLItFryJ0T8IgWUDrcTc" +
"ebC0UrnQyki/oRZelgtZgxgNBneidDaCjVnsNPhk/ASVXJrIntc0vXUSwCBnj9vAjlVw6b3RpYzk" +
"VKys+kbJdoScMlMMNtrjDdngopfQrfwM2OW90dEErYDNZIivsiUbYm3EpwuLD+cW+XGRHpeuqnQJ" +
"ypXLlk4gRx9AKmwAYmvyNOat1Hbv+wg/BaNIw/DCRrr9JeETfYz+iY/bP/IRqf5BpO/5V5JkfrkA" +
"jBsDeOkyTKLHyFS/s+A80ksOcDp9/1S77MyTEISo4fBY+4r+QKQucPZ2oeszClQPW6S+NvkCAAD/" +
"/wMAUEsDBBQABgAIAAAAIQC1VTAj9AAAAEwCAAALAAgCX3JlbHMvLnJlbHMgogQCKKAAAgAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAArJJNT8Mw" +
"DIbvSPyHyPfV3ZAQQkt3QUi7IVR+gEncD7WNoyQb3b8nHBBUGoMDR3+9fvzK2908jerIIfbiNKyL" +
"EhQ7I7Z3rYaX+nF1ByomcpZGcazhxBF21fXV9plHSnkodr2PKqu4qKFLyd8jRtPxRLEQzy5XGgkT" +
"pRyGFj2ZgVrGTVneYviuAdVCU+2thrC3N6Dqk8+bf9eWpukNP4g5TOzSmRXIc2Jn2a58yGwh9fka" +
"VVNoOWmwYp5yOiJ5X2RswPNEm78T/XwtTpzIUiI0Evgyz0fHJaD1f1q0NPHLnXnENwnDq8jwyYKL" +
"H6jeAQAA//8DAFBLAwQUAAYACAAAACEAu4FE2vAAAABHAwAAGgAIAXhsL19yZWxzL3dvcmtib29r" +
"LnhtbC5yZWxzIKIEASigAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAvJLNasMwEITvhbyD" +
"2Hu8ttOWEiLnUgq5lvQBhL3+IbYktNsfv32FC24Dwb2EXASjRTMfq9ntv4ZefVDgzlkNWZKCIlu6" +
"qrONhrfjy/oJFIuxlemdJQ0jMeyL1d3ulXoj8RG3nWcVXSxraEX8FpHLlgbDifNk46R2YTASZWjQ" +
"m/JkGsI8TR8x/PWA4sxTHSoN4VBtQB1HH5P/93Z13ZX07Mr3gaxciMBPF07cEkk0NaEh0TBfMU6T" +
"TRKJAS/D5DeGyZdgshvDZEswD9eEYRn72LP5j370Uvz9NeMltpd+0yeJ0zmvAM/qX3wDAAD//wMA" +
"UEsDBBQABgAIAAAAIQARQzLzpwEAACkDAAAPAAAAeGwvd29ya2Jvb2sueG1sjJJNb9swDIbvA/Yf" +
"BN0bWaoXBEGcAsM2LJehwLr2rMh0LEQfhiTXyb8fZcNJgPTQk0nq9cOXojZPJ2vIO4SovasoXxSU" +
"gFO+1u5Q0X8vvx5WlMQkXS2Nd1DRM0T6tP36ZTP4cNx7fyQIcLGibUrdmrGoWrAyLnwHDk8aH6xM" +
"mIYDi10AWccWIFnDRFEsmZXa0YmwDp9h+KbRCn541VtwaYIEMDKh/djqLs40qz6DszIc++5Bedsh" +
"Yq+NTucRSolV693B+SD3Bsc+8W8zGcM7tNUq+OibtEAUm0zezcsLxvk08nbTaAOv07UT2XV/pM1d" +
"DCVGxvSz1gnqii4x9QNcCyUloe++99rgKS9LUVC2vaziORDEJgjPQb9LdUYJJTU0sjfpBdcyN8S6" +
"KIVY5n/zCl81DPGKySk5vWlX+6GiosQncZ4zXuA9DOPRm65Tm12srrXfoA9tquiq4KMzdoMfF49t" +
"xi9x48B/82NAk2Ntl2fCAdcag7CrefZ3pxY3aowvavGh+vFGjfFF/ZjVbLakpFF4efkzmhBC8LE7" +
"m1/59j8AAAD//wMAUEsDBBQABgAIAAAAIQCDr+rjjQYAAOMbAAATAAAAeGwvdGhlbWUvdGhlbWUx" +
"LnhtbOxZzW4bNxC+F+g7EHtPLNmSYxmRA0uW4jZxYthKihypFbXLmLtckJQd3YrkWKBA0bTopUBv" +
"PRRtAyRAL+nTuE3RpkBeoUNyJZEWFduJgf7FBmyJ+3E4nJ+PM9yr1x5kDB0SISnPm1H1ciVCJI/5" +
"gOZJM7rT615ai5BUOB9gxnPSjMZERtc23n/vKl5XKckIgvm5XMfNKFWqWF9akjEMY3mZFySHZ0Mu" +
"Mqzgq0iWBgIfgdyMLS1XKqtLGaZ5hHKcgdjbwyGNCeppkdHGRHiHwddcST0QM7GvRRNvhsEODqoa" +
"IceyzQQ6xKwZwToDftQjD1SEGJYKHjSjivmJljauLuH1chJTC+Y687rmp5xXThgcLJs1RdKfLlrt" +
"1hpXtqbyDYCpeVyn02l3qlN5BoDjGHZqdXFl1rpr1dZEpgOyH+dltyv1Ss3HO/JX5nRutFqteqPU" +
"xQo1IPuxNodfq6zWNpc9vAFZfH0OX2tttturHt6ALH51Dt+90lit+XgDShnND+bQ2qHdbil9Chly" +
"th2ErwF8rVLCZyiIhml06SWGPFeLYi3D97noAkADGVY0R2pckCGOIYrbOOsLiiNU4JxLGKgsV7qV" +
"Ffirf2vmU00vj9cJdubZoVjODWlNkIwFLVQz+hCkRg7k1fPvXz1/il49f3L88Nnxw5+OHz06fvij" +
"leVN3MZ54k58+e1nf379Mfrj6TcvH38RxksX/+sPn/zy8+dhIOTXbP8vvnzy27MnL7769PfvHgfg" +
"mwL3XXiPZkSiW+QI7fEM9mYM42tO+uJ8M3oppt4MnILsgOiOSj3grTFmIVyL+Ma7K4BaQsDro/ue" +
"rvupGCkaWPlGmnnAHc5Zi4ugAW7otRwL90Z5El5cjFzcHsaHobXbOPdc2xkVwKkQsvO2b6fEU3OX" +
"4VzhhOREIf2MHxASmHaPUs+uOzQWXPKhQvcoamEaNEmP9r1Amk3aphn4ZRxSEFzt2WbnLmpxFtr1" +
"Fjn0kZAQmAWU7xHmmfE6HimchUT2cMZcg9/EKg0puT8WsYvrSAWeTgjjqDMgUobm3BawX8fpNzCw" +
"WdDtO2yc+Uih6EFI5k3MuYvc4gftFGdFUGeapy72A3kAIYrRLlch+A73M0R/Bz/gfKG771Liuft0" +
"IrhDE0+lWYDoJyMR8OV1wv18HLMhJoZlgPA9Hs9o/jpSZxRY/QSp19+Ruj2VTpL6JhyAodTaPkHl" +
"i3D/QgLfwqN8l0DOzJPoO/5+x9/Rf56/F+XyxbP2jKiBw2d1uqnas4VF+5Aytq/GjNyUpm6XcDwN" +
"ujBoGgrTVU6buCKFj2WL4OESgc0cJLj6iKp0P8UFlPhV04ImshSdSFRwCZW/GTbNMDkh27S3FAp7" +
"06nWdQ9jmUNitcMHdnjF7VWnYkznmph+eLLQihZw1sVWrrzdYlWr1UKz+VurGtUMKXpbm24ZfDi/" +
"NRicWhPqHgTVElh5Fa4MtO7QDWFGBtruto+fuEUvfaEukikekNJHet/zPqoaJ01iZRJGAR/pvvMU" +
"HzmrNbTYt1jtLE5yl6stWG7ivbfx0qTZnnlJ5+2JdGS5m5wsR0fNqFFfrkcoxkUzGkKbDR+zArwu" +
"damJWQJ3VbESNuxPTWYTrjNvNsJhWYWbE2v3uQ17PFAIqbawTG1omEdlCLDcXAoY/ZfrYNaL2oCN" +
"9DfQYmUNguFv0wLs6LuWDIckVq6znRFzK2IAJZXykSJiPx0coT4biT0M7tehCvsZUAn3IYYR9Be4" +
"2tPWNo98ci6Tzr1QMzg7jlmR4pJudYpOMtnCTR5PdTDfrLZGPdhbUHezufNvxaT8BW3FDeP/2Vb0" +
"eQIXFCsD7YEYbpYFRjpfmxEXKuXAQkVK466AazXDHRAtcD0MjyGo4H7b/BfkUP+3OWdlmLSGPlPt" +
"0QQJCueRSgUhu0BLJvpOEVYtzy4rkpWCTEQ56srCqt0nh4T1NAeu6rM9QimEumGTkgYM7mT8+d/L" +
"DOonusj5p1Y+NpnPWx7o6sCWWHb+GWuRmkP6zlHQCJ59pqaa0sFrDvZzHrWWseZ2vFw/81FbwDUT" +
"3C4riImYipjZlyX6QO3xPeBWBO8+bHmFIKov2cIDaYK09NiHwskO2mDSomzBUla3F15GwQ15WelO" +
"14UsfZNK95zGnhZn/nJeLr6++jyfsUsLe7Z2K92AqSFpT6aoLo8mjYxxjHnL5r4I4/374OgteOUw" +
"YkralwkP4FIRugz70gKS3zrXTN34CwAA//8DAFBLAwQUAAYACAAAACEAfsGK5WABAAB0AgAAGAAA" +
"AHhsL3dvcmtzaGVldHMvc2hlZXQyLnhtbIySwWrDMAyG74O9g/G9cdqt2xqSlEEp62Ewxra74yiJ" +
"aWwF213bt5+SkDLopTcJSZ9//XK6PpmW/YLzGm3G51HMGViFpbZ1xr+/trMXznyQtpQtWsj4GTxf" +
"5/d36RHd3jcAgRHB+ow3IXSJEF41YKSPsANLlQqdkYFSVwvfOZDlMGRasYjjJ2GktnwkJO4WBlaV" +
"VrBBdTBgwwhx0MpA+n2jOz/RjLoFZ6TbH7qZQtMRotCtDucByplRya626GTR0t6n+aNUE3tIrvBG" +
"K4ceqxARToxCr3deiZUgUp6WmjbobWcOqoy/zrnI08GcHw1H/y9mvdcF4r4v7MqMx32ruOrdDl5/" +
"OFZCJQ9t+MTjG+i6CXTYJWnvV0jK8wa8Iu8IEy2Wl0c3MkiidrKGd+lqbT1roRq6njlzIyaOKA7Y" +
"9bPPhCwwBDRT1tB1ga4YRw+cVYhhSnq1l/+S/wEAAP//AwBQSwMEFAAGAAgAAAAhAH7BiuVgAQAA" +
"dAIAABgAAAB4bC93b3Jrc2hlZXRzL3NoZWV0My54bWyMksFqwzAMhu+DvYPxvXHardsakpRBKeth" +
"MMa2u+MoiWlsBdtd27efkpAy6KU3CUmff/1yuj6Zlv2C8xptxudRzBlYhaW2dca/v7azF858kLaU" +
"LVrI+Bk8X+f3d+kR3d43AIERwfqMNyF0iRBeNWCkj7ADS5UKnZGBUlcL3zmQ5TBkWrGI4ydhpLZ8" +
"JCTuFgZWlVawQXUwYMMIcdDKQPp9ozs/0Yy6BWek2x+6mULTEaLQrQ7nAcqZUcmutuhk0dLep/mj" +
"VBN7SK7wRiuHHqsQEU6MQq93XomVIFKelpo26G1nDqqMv865yNPBnB8NR/8vZr3XBeK+L+zKjMd9" +
"q7jq3Q5efzhWQiUPbfjE4xvougl02CVp71dIyvMGvCLvCBMtlpdHNzJIonayhnfpam09a6Eaup45" +
"cyMmjigO2PWzz4QsMAQ0U9bQdYGuGEcPnFWIYUp6tZf/kv8BAAD//wMAUEsDBBQABgAIAAAAIQDi" +
"2BZGtwEAAH4DAAAYAAAAeGwvd29ya3NoZWV0cy9zaGVldDEueG1slFPLbtswELwX6D8QvEeUlDhp" +
"BElBGiNoDgWKvu4UtZIIi1yBpO3477sSa6O1USC9cYfcmZ1ZqXx4NSPbgfMabcWzJOUMrMJW277i" +
"P74/X33gzAdpWzmihYofwPOH+v27co9u4weAwIjB+ooPIUyFEF4NYKRPcAJLNx06IwOVrhd+ciDb" +
"pcmMIk/TW2GktjwyFO4tHNh1WsEa1daADZHEwSgDze8HPfkjm1FvoTPSbbbTlUIzEUWjRx0OCyln" +
"RhUvvUUnm5F8v2Y3Uh25l+KC3mjl0GMXEqITcdBLz/fiXhBTXbaaHMyxMwddxR+z4innoi6XfH5q" +
"2Ps/zizI5huMoAK0tCbO5vgbxM388IWgdG4VF73PS/xfHGuhk9sxfMX9J9D9EIhkRXZmV0V7WINX" +
"FCfRJPnqNMRaBlmXDveMVkOafpLzorPi+l+ddanmt48Z+dvVWSl2NJT6jX6MaP43+hTR6xMqSPCk" +
"mv+Par6o3pypRnR1phrR2zPVGGD0PckePkvXa+vZCN0Szh1nLqaXJnQOOM2R3VGSDYaA5lgN9J0D" +
"BZEmFFWHGI7FvKTTn1P/AgAA//8DAFBLAwQUAAYACAAAACEAi9BfMoYCAACxBQAADQAAAHhsL3N0" +
"eWxlcy54bWykVG1vmzAQ/j5p/8Hyd2qgIQsRUC1NkSp106R20r46YBKrfkG26ZJN++89A0moOm3T" +
"+gXfHefnnntzdrWXAj0xY7lWOY4uQoyYqnTN1TbHXx/KYIGRdVTVVGjFcnxgFl8V799l1h0Eu98x" +
"5hBAKJvjnXPtkhBb7Zik9kK3TMGfRhtJHahmS2xrGK2tvyQFicNwTiTlCg8IS1n9C4ik5rFrg0rL" +
"ljq+4YK7Q4+FkayWt1ulDd0IoLqPZrQ6YvfKK3jJK6OtbtwFwBHdNLxir1mmJCWAVGSNVs6iSnfK" +
"Qa0A2kdYPir9XZX+lzcOXkVmf6AnKsASYVJklRbaIAeVAWK9RVHJBo9rKvjGcO/WUMnFYTDH3tAX" +
"c/STHFLzRuJ5jIeFS1yIE6vYEwBDkUF1HDOqBAWN8sOhhfAKGjnA9H5/8d4aeojiZHKB9AGLbKNN" +
"DYNzrsfRVGSCNQ6IGr7d+dPpFr4b7RxUuchqTrdaUeFTGUBOAqRTMSHu/XB9a15g7xukOllKd1vn" +
"GMbUF+EoQiKjOOANisefog3Yb4ZF++YlPiBOaL8gfQqPfL9z/Nlvg4DJGSHQpuPCcfUbwoBZ788l" +
"CH0HnJ/svjinKFCJmjW0E+7h9DPHZ/kTq3kn45PXF/6kXQ+R47M8eKU+Btu7OwvjBSfqDM/xz5vV" +
"h3R9U8bBIlwtgtklS4I0Wa2DZHa9Wq/LNIzD61+TRXvDmvXPQZHBYi2tgGU0Y7JjivdnW44nyp0f" +
"tH6tCNCeck/jefgxicKgvAyjYDani2Axv0yCMoni9Xy2uknKZMI9+T/uUUiiaHjLPPlk6bhkgqtj" +
"r44dmlqhSaD+IQmfSt8Jcn5ri2cAAAD//wMAUEsDBBQABgAIAAAAIQAUBanLPQEAAFECAAARAAgB" +
"ZG9jUHJvcHMvY29yZS54bWwgogQBKKAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB8klFL" +
"wzAUhd8F/0PJe5vE4tDQdqCyJweCFcW3kNxtwSYNSbTbvzdtt9rBEPKSe8797sklxXKvm+QHnFet" +
"KRHNCErAiFYqsy3RW71K71DiAzeSN62BEh3Ao2V1fVUIy0Tr4MW1FlxQ4JNIMp4JW6JdCJZh7MUO" +
"NPdZdJgoblqneYhXt8WWiy++BXxDyAJrCFzywHEPTO1EREekFBPSfrtmAEiBoQENJnhMM4r/vAGc" +
"9hcbBmXm1CocbHzTMe6cLcUoTu69V5Ox67qsy4cYMT/FH+vn1+GpqTL9rgSgqpCCCQc8tK4q8PwS" +
"F9dwH9ZxxxsF8uEQ9Qs1KYa4IwRkEgOwMe5Jec8fn+oVqvodpuQ+pYuaEDacz37kWX8faCzo4+B/" +
"iTTviSSvyYLdUkbpjHgCjLnPP0H1CwAA//8DAFBLAwQUAAYACAAAACEAaS/Zj5QBAABHAwAAEAAI" +
"AWRvY1Byb3BzL2FwcC54bWwgogQBKKAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" +
"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACck0Fv" +
"2zAMhe8D9h8M3Rs5yTYMgaxiSDf0sGEBkrZnTaZjobYkiKyR7NePtlHX6dbLbhTfw8MnUlLXp7bJ" +
"Okjogi/EcpGLDLwNpfPHQtwdvl19FhmS8aVpgodCnAHFtX7/Tu1SiJDIAWYc4bEQNVHcSIm2htbg" +
"gmXPShVSa4iP6ShDVTkLN8E+teBJrvL8k4QTgS+hvIpToBgTNx39b2gZbM+H94dzZGCtvsTYOGuI" +
"b6l/OJsChoqyrycLjZJzUTHdHuxTcnTWuZLzo9pb08CWg3VlGgQlXxrqFkw/tJ1xCbXqaNOBpZAy" +
"dL95bCuR/TIIPU4hOpOc8cRYvW08DHUTkZJ+COkRawBCJdkwNody7p3X7oNeDwYuLo19wAjCwiXi" +
"wVED+LPamUT/IF7PiQeGkXfE2fd8yznfRDpIq7elkXR+q2FQzPeKaBvaaPyZhan67vwj3sVDuDEE" +
"z0u4bKp9bRKUvLdpSVND3fL8U9OHbGvjj1A+e/4W+idzP/4Lvfy4yNc5v4ZZT8mXH6D/AAAA//8D" +
"AFBLAQItABQABgAIAAAAIQD21qXvWgEAABgFAAATAAAAAAAAAAAAAAAAAAAAAABbQ29udGVudF9U" +
"eXBlc10ueG1sUEsBAi0AFAAGAAgAAAAhALVVMCP0AAAATAIAAAsAAAAAAAAAAAAAAAAAkwMAAF9y" +
"ZWxzLy5yZWxzUEsBAi0AFAAGAAgAAAAhALuBRNrwAAAARwMAABoAAAAAAAAAAAAAAAAAuAYAAHhs" +
"L19yZWxzL3dvcmtib29rLnhtbC5yZWxzUEsBAi0AFAAGAAgAAAAhABFDMvOnAQAAKQMAAA8AAAAA" +
"AAAAAAAAAAAA6AgAAHhsL3dvcmtib29rLnhtbFBLAQItABQABgAIAAAAIQCDr+rjjQYAAOMbAAAT" +
"AAAAAAAAAAAAAAAAALwKAAB4bC90aGVtZS90aGVtZTEueG1sUEsBAi0AFAAGAAgAAAAhAH7BiuVg" +
"AQAAdAIAABgAAAAAAAAAAAAAAAAAehEAAHhsL3dvcmtzaGVldHMvc2hlZXQyLnhtbFBLAQItABQA" +
"BgAIAAAAIQB+wYrlYAEAAHQCAAAYAAAAAAAAAAAAAAAAABATAAB4bC93b3Jrc2hlZXRzL3NoZWV0" +
"My54bWxQSwECLQAUAAYACAAAACEA4tgWRrcBAAB+AwAAGAAAAAAAAAAAAAAAAACmFAAAeGwvd29y" +
"a3NoZWV0cy9zaGVldDEueG1sUEsBAi0AFAAGAAgAAAAhAIvQXzKGAgAAsQUAAA0AAAAAAAAAAAAA" +
"AAAAkxYAAHhsL3N0eWxlcy54bWxQSwECLQAUAAYACAAAACEAFAWpyz0BAABRAgAAEQAAAAAAAAAA" +
"AAAAAABEGQAAZG9jUHJvcHMvY29yZS54bWxQSwECLQAUAAYACAAAACEAaS/Zj5QBAABHAwAAEAAA" +
"AAAAAAAAAAAAAAC4GwAAZG9jUHJvcHMvYXBwLnhtbFBLBQYAAAAACwALAMoCAACCHgAAAAA=";

        var xlsx = new openXml.OpenXmlPackage();
        xlsx.openFromBase64Async(templateSpreadsheet, function (openedSpreadsheet) {

            var workbookPart = openedSpreadsheet.workbookPart();
            var wbXDoc = workbookPart.getXDocument();
            var sheetElement = wbXDoc.root
                .element(S.sheets)
                .elements(S.sheet)
                .firstOrDefault(function (sh) {
                    return sh.attribute("name").value === 'Sheet1';
                });
            if (sheetElement) {
                var id = sheetElement.attribute(R.id).value;
                var worksheetPart = workbookPart.getPartById(id);
                var wsXDoc = worksheetPart.getXDocument();
                var firstCell = wsXDoc.descendants(S.c).firstOrDefault();
                if (firstCell) {
                    var newFirstCell = new XElement(S.c,
                        firstCell.attribute("r"),
                        new XElement(S.v, "1000"));
                    firstCell.replaceWith(newFirstCell);
                }
            }

            openedSpreadsheet.saveToBlobAsync(function (blob) {
                spreadsheetToSave = blob;
            });
        });

        root.saveDocument = function () {
            if (spreadsheetToSave === null) {
                alert("Spreadsheet is not open");
            }
            else {
                saveAs(spreadsheetToSave, "SavedSpreadsheet.xlsx");
            }
        }

    }(this));

</script>
</pre>
    <div id="testContent"></div>
</body>
</html>
