﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Add a comment</title>
</head>
<body>
    <form>
        <p id="jsupload" style="height: 30px;">
            You must have Flash 10 installed to download a file.
        </p>
        <input id="btnModify" type="button" onclick="modifyDocument()" value="Modify" style="height: 30px; width: 100px; background-color: rgb(212,208,200); border: thin solid black;" />
        <p id="jsdownload" style="height: 30px;">
            You must have Flash 10 installed to download a file.
        </p>
    </form>
    <pre>
<script type="text/javascript" src="linq.js"></script>
<script type="text/javascript" src="ltxml.js"></script>
<script type="text/javascript" src="ltxml-extensions.js"></script>
<script type="text/javascript" src="jszip.js"></script>
<script type="text/javascript" src="jszip-load.js"></script>
<script type="text/javascript" src="jszip-inflate.js"></script>
<script type="text/javascript" src="jszip-deflate.js"></script>
<script type="text/javascript" src="openxml.js"></script>
<script type="text/javascript" src="swfobject.js"></script>
<script type="text/javascript" src="jsupload.js"></script>
<script type="text/javascript" src="jsdownload.js"></script>

<script type="text/javascript">
    /***************************************************************************
    
    Copyright (c) Microsoft Corporation 2013.
    
    This code is licensed using the Microsoft Public License (Ms-PL).  You can find the text of the license here:
    
    http://www.microsoft.com/resources/sharedsource/licensingbasics/publiclicense.mspx
    
    Published at http://OpenXmlDeveloper.org
    Resource Center and Documentation: http://openxmldeveloper.org/wiki/w/wiki/open-xml-sdk-for-javascript.aspx
    
    Developer: Eric White
    Blog: http://www.ericwhite.com
    Twitter: @EricWhiteDev
    Email: <EMAIL>
    
    ***************************************************************************/

    var modifyDocument = null;

    (function (root) {  // root = global
        "use strict";

        var XAttribute = Ltxml.XAttribute;
        var XCData = Ltxml.XCData;
        var XComment = Ltxml.XComment;
        var XContainer = Ltxml.XContainer;
        var XDeclaration = Ltxml.XDeclaration;
        var XDocument = Ltxml.XDocument;
        var XElement = Ltxml.XElement;
        var XName = Ltxml.XName;
        var XNamespace = Ltxml.XNamespace;
        var XNode = Ltxml.XNode;
        var XObject = Ltxml.XObject;
        var XProcessingInstruction = Ltxml.XProcessingInstruction;
        var XText = Ltxml.XText;
        var XEntity = Ltxml.XEntity;
        var cast = Ltxml.cast;
        var castInt = Ltxml.castInt;

        var W = openXml.W;
        var NN = openXml.NoNamespace;
        var wNs = openXml.wNs;

        var openedFileName = null;
        var openedFileData = null;

        root.modifyDocument = function () {
            if (openedFileName === null || openedFileData === null) {
                alert("You must open a file first.");
            }
            else {
                /******************************************************************************/
                var addCommentPartIfNecessary = function (doc) {
                    var mainPart = doc.mainDocumentPart();
                    var commentPart = mainPart.wordprocessingCommentsPart();
                    if (commentPart) {
                        return;
                    }
                    var comments = new XDocument(
                        new XElement(W.comments,
                            new XAttribute(XNamespace.xmlns + "w", wNs.namespaceName)));
                    var newCommentPart = doc.addPart("/comments.xml",
                        openXml.contentTypes.wordprocessingComments,
                        "xml", comments);
                    mainPart.addRelationship("rId9999",
                        openXml.relationshipTypes.wordprocessingComments,
                        "../comments.xml", "Internal");
                }

                // Open the document.
                var doc = new openXml.OpenXmlPackage(openedFileData);

                addCommentPartIfNecessary(doc);

                // Determine the next comment ID.
                var commentsPart = doc.mainDocumentPart().wordprocessingCommentsPart();
                var commentsPartXDoc = commentsPart.getXDocument();
                var nextCommentId = 1;
                if (new XEnumerable(commentsPartXDoc.elements()).elements(W.comment).any()) {
                    nextCommentId = commentsPartXDoc
                        .root
                        .elements(W.comment)
                        .select(function (c) {
                            return castInt(c.attribute(W.id));
                        })
                        .max() + 1;
                }

                // Create the comment element and add it.
                var comment = new XElement(W.comment,
                    new XAttribute(W.id, nextCommentId),
                    new XElement(W.p,
                        new XElement(W.r,
                            new XElement(W.t, "This is a new important comment."))));
                commentsPartXDoc.root.add(comment);

                // Add a paragraph at the beginning of the document.  The new paragraph contains a comment.
                var mainPart = doc.mainDocumentPart();
                var mainPartXDoc = mainPart.getXDocument();
                var newFirstPara = new XElement(W.p,
                    new XElement(W.commentRangeStart,
                        new XAttribute(W.id, nextCommentId)),
                    new XElement(W.r,
                        new XElement(W.t, "This is a paragraph that contains a comment.")),
                    new XElement(W.r,
                        new XElement(W.commentReference,
                            new XAttribute(W.id, nextCommentId))),
                    new XElement(W.commentRangeEnd,
                        new XAttribute(W.id, nextCommentId)));
                mainPartXDoc.root.element(W.body).addFirst(newFirstPara);

                // serialize it as base64
                openedFileData = doc.saveToBase64();

                alert("Added comments part if necessary.  Added a paragraph that contains a comment.");
                /******************************************************************************/
            }
        }

        JsUpload.create('jsupload', {
            onComplete: function (fileName, theData) {
                openedFileName = fileName;
                openedFileData = theData;
            },
            onCancel: function () { alert('Canceled'); },
            swf: 'media/JsUpload.swf',
            uploadImage: 'images/upload.png',
            width: 100,
            height: 30,
            transparent: true,
            append: false,
            fileFilterDescription: "All",
            fileFilterWildcards: "*.docx"
        });

        JsDownload.create('jsdownload', {
            data: function () {
                return openedFileData;
            },
            filename: function () {
                return openedFileName;
            },
            dataType: "base64",
            onComplete: function () {
                /* alert('Saved'); */
            },
            onCancel: function () {
                alert('Canceled');
            },
            onError: function () {
                alert('Empty file, not saved');
            },
            swf: 'media/JsDownload.swf',
            downloadImage: 'images/download.png',
            width: 100,
            height: 30,
            transparent: true,
            append: false
        });
    }(this));

</script>
</pre>
</body>
</html>
